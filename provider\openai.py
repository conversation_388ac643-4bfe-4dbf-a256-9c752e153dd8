import requests


class OpenAI:
    def __init__(self):
        self.provider="OpenAI"
        self.regex = [
            "sk-[A-Za-z0-9\-]{20,}",
            "sk-proj-[A-Za-z0-9\-]{20,}",
            "sk-svcacct-[A-Za-z0-9\-]{20,}",
            "sk-[A-Za-z0-9]{48}",  # Standard format
            "Bearer sk-[A-Za-z0-9\-]{20,}"  # Keys in auth headers
        ]
        self.url = "https://api.openai.com/v1/models"

    def get_regex(self):
        return self.regex

    def test(self, api_key):
        headers = {
            "Authorization": f"Bearer {api_key}"
        }
        response = requests.get(self.url, headers=headers)
        return response.status_code == 200
