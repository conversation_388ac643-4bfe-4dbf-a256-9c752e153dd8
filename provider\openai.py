"""
OpenAI API key provider implementation.
"""
import logging
import requests
from providers.base import BaseProvider, ValidationResult
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


logger = logging.getLogger(__name__)


class OpenAIProvider(BaseProvider):
    """Provider for OpenAI API key validation."""

    def __init__(self):
        super().__init__("OpenAI")

    @property
    def regex_patterns(self):
        """OpenAI API key patterns."""
        return [
            r"sk-[A-Za-z0-9\-]{20,}",
            r"sk-proj-[A-Za-z0-9\-]{20,}",
            r"sk-svcacct-[A-Za-z0-9\-]{20,}",
            r"sk-[A-Za-z0-9]{48}",  # Standard format
            r"Bearer\s+sk-[A-Za-z0-9\-]{20,}"  # Keys in auth headers
        ]

    @property
    def test_endpoint(self):
        """OpenAI API endpoint for testing."""
        return "https://api.openai.com/v1/models"

    def validate_key(self, api_key: str) -> ValidationResult:
        """
        Validate OpenAI API key by making a test request.

        Args:
            api_key: The API key to validate

        Returns:
            ValidationResult object
        """
        if not self.matches_pattern(api_key):
            return ValidationResult(
                is_valid=False,
                status="invalid",
                error_message="API key format does not match OpenAI patterns",
                provider=self.name
            )

        # Clean the API key
        clean_key = api_key.strip()
        if clean_key.lower().startswith('bearer '):
            clean_key = clean_key[7:].strip()

        headers = {
            "Authorization": f"Bearer {clean_key}",
            "User-Agent": "API-Key-Scanner/1.0"
        }

        try:
            response = self._make_test_request(clean_key, headers=headers)
            return self._handle_response(response, api_key)

        except requests.exceptions.Timeout:
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message="Request timeout",
                provider=self.name
            )
        except requests.exceptions.RequestException as e:
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message=f"Request failed: {str(e)}",
                provider=self.name
            )
        except Exception as e:
            self.logger.error(f"Unexpected error validating OpenAI key: {e}")
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message=f"Validation error: {str(e)}",
                provider=self.name
            )


# Legacy compatibility
class OpenAI(OpenAIProvider):
    """Legacy OpenAI class for backward compatibility."""

    def __init__(self):
        super().__init__()
        self.provider = "OpenAI"
        self.regex = [pattern.replace(r'\s+', r'\s*')
                      for pattern in self.regex_patterns]
        self.url = self.test_endpoint

    def get_regex(self):
        return self.regex

    def test(self, api_key):
        result = self.validate_key(api_key)
        return result.is_valid
