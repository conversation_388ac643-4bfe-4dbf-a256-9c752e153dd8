#!/usr/bin/env python3
"""
Test script to verify the API key scanner installation.
"""
import sys
import os
import tempfile
import shutil

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test core modules
        from utils.config import ConfigManager
        from utils.logging import setup_logging, get_logger
        from database.models import APIKey, Query, APIKeyStatus
        from database.connection import DatabaseManager
        from providers.base import BaseProvider, ValidationResult
        from providers.openai import OpenAIProvider
        from providers.gemini import GeminiProvider
        from services.scanner import GitHubScanner
        from services.verifier import APIKeyVerifier
        from utils.retry import retry_with_backoff, github_retry, api_retry
        
        print("✓ All imports successful")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False


def test_configuration():
    """Test configuration management."""
    print("Testing configuration...")
    
    try:
        from utils.config import ConfigManager
        
        # Create temporary config file
        config_data = {
            "database": {"path": "test.db"},
            "github": {"tokens": ["test_token"]},
            "scanner": {"loop": False, "interval": 60},
            "verifier": {"loop": False, "interval": 30},
            "logging": {"level": "INFO"}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            import json
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            assert config_manager.get('database', 'path') == 'test.db'
            assert config_manager.get('github', 'tokens') == ['test_token']
            print("✓ Configuration management working")
            return True
        finally:
            os.unlink(config_file)
            
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False


def test_database():
    """Test database functionality."""
    print("Testing database...")
    
    try:
        from database.connection import DatabaseManager
        from database.models import APIKey, Query, APIKeyStatus
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        try:
            db_manager = DatabaseManager(db_path)
            db_manager.initialize()
            
            # Test session creation
            with db_manager.get_session() as session:
                # Test creating a query
                test_query = Query(
                    query="test query",
                    description="Test query",
                    is_active=True
                )
                session.add(test_query)
                session.commit()
                
                # Test querying
                queries = session.query(Query).all()
                assert len(queries) == 1
                assert queries[0].query == "test query"
            
            print("✓ Database functionality working")
            return True
            
        finally:
            if os.path.exists(db_path):
                os.unlink(db_path)
                
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False


def test_providers():
    """Test provider functionality."""
    print("Testing providers...")
    
    try:
        from providers.openai import OpenAIProvider
        from providers.gemini import GeminiProvider
        
        # Test OpenAI provider
        openai_provider = OpenAIProvider()
        assert openai_provider.name == "OpenAI"
        assert len(openai_provider.regex_patterns) > 0
        assert openai_provider.test_endpoint.startswith("https://")
        
        # Test pattern matching
        test_key = "sk-1234567890abcdef1234567890abcdef"
        assert openai_provider.matches_pattern(test_key)
        
        # Test Gemini provider
        gemini_provider = GeminiProvider()
        assert gemini_provider.name == "Gemini"
        assert len(gemini_provider.regex_patterns) > 0
        
        # Test pattern matching
        test_gemini_key = "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI"
        assert gemini_provider.matches_pattern(test_gemini_key)
        
        print("✓ Provider functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Provider error: {e}")
        return False


def test_logging():
    """Test logging functionality."""
    print("Testing logging...")
    
    try:
        from utils.logging import setup_logging, get_logger
        
        # Test logging setup
        config = {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
        
        setup_logging(config)
        logger = get_logger("test")
        
        # Test logging (should not raise exceptions)
        logger.info("Test log message")
        logger.warning("Test warning message")
        logger.error("Test error message")
        
        print("✓ Logging functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Logging error: {e}")
        return False


def test_retry_logic():
    """Test retry functionality."""
    print("Testing retry logic...")
    
    try:
        from utils.retry import retry_with_backoff, ExponentialBackoff
        
        # Test exponential backoff
        backoff = ExponentialBackoff(base_delay=1.0, max_delay=10.0)
        delay1 = backoff.get_delay(0)
        delay2 = backoff.get_delay(1)
        delay3 = backoff.get_delay(2)
        
        assert 0.5 <= delay1 <= 1.5  # With jitter
        assert delay2 > delay1
        assert delay3 > delay2
        
        # Test retry decorator
        call_count = 0
        
        @retry_with_backoff(max_attempts=3, base_delay=0.1, exceptions=(ValueError,))
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Test error")
            return "success"
        
        result = test_function()
        assert result == "success"
        assert call_count == 3
        
        print("✓ Retry logic working")
        return True
        
    except Exception as e:
        print(f"✗ Retry logic error: {e}")
        return False


def main():
    """Run all tests."""
    print("API Key Scanner Installation Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_configuration,
        test_database,
        test_providers,
        test_logging,
        test_retry_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Installation appears to be working correctly.")
        return 0
    else:
        print("✗ Some tests failed. Please check the installation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
