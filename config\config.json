{"database": {"path": "data/api_keys.db", "pool_size": 10, "max_overflow": 20}, "github": {"tokens": ["*********************************************************************************************", "****************************************"], "rate_limit_buffer": 100}, "scanner": {"loop": false, "interval": 60, "max_retries": 3, "batch_size": 100, "thread_count": 3}, "verifier": {"loop": false, "interval": 30, "max_retries": 3, "batch_size": 50, "thread_count": 5}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/api_scanner.log"}}