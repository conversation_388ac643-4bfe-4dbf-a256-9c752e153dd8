2025-08-13 19:21:12 - root - INFO - Logging configuration initialized
2025-08-13 19:21:12 - __main__ - INFO - Initializing database...
2025-08-13 19:21:12 - database.connection - ERROR - Failed to initialize database: (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-13 19:28:07 - root - INFO - Logging configuration initialized
2025-08-13 19:28:07 - __main__ - INFO - Creating database...
2025-08-13 19:28:07 - __main__ - INFO - Creating new database at: data/api_keys.db
2025-08-13 19:28:07 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:28:07 - __main__ - INFO - Database creation completed successfully
2025-08-13 19:32:42 - root - INFO - Logging configuration initialized
2025-08-13 19:32:42 - __main__ - INFO - Creating database...
2025-08-13 19:32:42 - __main__ - INFO - Database already exists at: data/api_keys.db
2025-08-13 19:32:42 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:32:42 - __main__ - INFO - Database creation completed successfully
2025-08-13 19:33:07 - root - INFO - Logging configuration initialized
2025-08-13 19:33:07 - __main__ - INFO - Initializing database...
2025-08-13 19:33:07 - __main__ - INFO - Database already exists at: data/api_keys.db
2025-08-13 19:33:07 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:33:07 - __main__ - INFO - Adding 32 sample queries...
2025-08-13 19:33:07 - __main__ - INFO - Successfully added 32 new queries to the database
2025-08-13 19:33:07 - __main__ - INFO - Database initialization completed
2025-08-13 19:33:40 - root - INFO - Logging configuration initialized
2025-08-13 19:33:40 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:33:40 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:33:40 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:33:40 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:33:40 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:33:40 - services.scanner - INFO - Processing 32 queries | service=scanner
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114dd0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efbf0f920> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114da0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114d70> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114d40> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114e00> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114e30> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114e90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114ef0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114f50> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc114fb0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115010> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115070> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1150d0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115130> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115190> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1151f0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115280> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1152e0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115340> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1153a0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115400> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115460> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1154c0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115520> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115580> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1155e0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115640> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1156a0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115700> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc115760> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x22efc1157c0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:33:40 - __main__ - ERROR - Scanner error: Instance <Query at 0x22efc114dd0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - root - INFO - Logging configuration initialized
2025-08-13 19:40:17 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:40:17 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:40:17 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:40:17 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:40:17 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:40:17 - services.scanner - INFO - Processing 32 queries | service=scanner
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad5115df10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4d10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4cb0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4c80> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4d40> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4d70> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4dd0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4e30> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4e90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4ef0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4f50> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e4fb0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5010> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5070> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e50d0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5160> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e51c0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5220> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5280> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e52e0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5340> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e53a0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5400> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5460> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e54c0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5520> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5580> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e55e0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5640> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e56a0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x2ad512e5700> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:17 - __main__ - ERROR - Scanner error: Instance <Query at 0x2ad5115df10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:40 - root - INFO - Logging configuration initialized
2025-08-13 19:40:40 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:40:41 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:40:41 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:40:41 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:40:41 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:40:41 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-13 19:40:41 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x18131ba4ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:41 - utils.retry - ERROR - Non-retryable error in _process_query: Instance <Query at 0x18131947710> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:40:41 - __main__ - ERROR - Scanner error: Instance <Query at 0x18131ba4ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-08-13 19:41:31 - root - INFO - Logging configuration initialized
2025-08-13 19:41:31 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:41:31 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:41:31 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:41:31 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:41:31 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:41:31 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-13 19:41:31 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-13 19:41:31 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:41:59 - root - INFO - Logging configuration initialized
2025-08-13 19:41:59 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:41:59 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:41:59 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:41:59 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:41:59 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:41:59 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-13 19:41:59 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-13 19:41:59 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:42:08 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:08 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D791:1BD42F:1A5235:1FDDFA:689C7A11 and timestamp 2025-08-13 11:42:09 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.95 seconds...
2025-08-13 19:42:08 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-13 19:42:09 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:42:10 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-08-13 19:42:10 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 100, 'found_keys': 74, 'saved_keys': 74} | service=scanner
2025-08-13 19:42:11 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:11 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D7C2:2029EC:1AC163:204CD5:689C7A14 and timestamp 2025-08-13 11:42:12 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 3.61 seconds...
2025-08-13 19:42:11 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-13 19:42:13 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-13 19:42:13 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D7B7:2C7476:1D4537:22D1CA:689C7A16 and timestamp 2025-08-13 11:42:14 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.24 seconds...
2025-08-13 19:42:13 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-13 19:42:14 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:42:14 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-13 19:42:16 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:16 - services.scanner - ERROR - Error processing query 'OPENAI_API_KEY language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-13 19:42:16 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-13 19:42:17 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-13 19:42:17 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D7CA:19E35D:1ADF06:206B9A:689C7A19 and timestamp 2025-08-13 11:42:17 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 2.09 seconds...
2025-08-13 19:42:17 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-13 19:42:19 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-13 19:42:20 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:20 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D7B6:2465CA:1A047D:1F9052:689C7A1D and timestamp 2025-08-13 11:42:21 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.63 seconds...
2025-08-13 19:42:20 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-13 19:42:20 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-13 19:42:20 - services.scanner - ERROR - Error processing query 'sk-proj- language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-13 19:42:22 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:42:23 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:23 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID D7D7:32CD0D:19C6A1:1F5267:689C7A20 and timestamp 2025-08-13 11:42:24 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 2.81 seconds...
2025-08-13 19:42:23 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-13 19:42:25 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:42:27 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-13 19:42:27 - services.scanner - ERROR - Error processing query 'OPENAI_API_KEY language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-13 19:42:27 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-13 19:45:29 - root - INFO - Logging configuration initialized
2025-08-13 19:45:29 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:45:29 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:45:51 - root - INFO - Logging configuration initialized
2025-08-13 19:45:51 - __main__ - INFO - API Key Scanner starting up...
2025-08-13 19:45:51 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-13 19:45:51 - __main__ - INFO - Starting GitHub scanner...
2025-08-13 19:45:51 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-13 19:45:51 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-13 19:45:51 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-13 19:45:51 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-13 19:45:51 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-13 19:45:52 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-13 11:46:53 | service=scanner | query_id=1
2025-08-13 19:45:52 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-13 11:46:53 | service=scanner | query_id=5
2025-08-13 19:45:53 - services.scanner - DEBUG - GitHub search result: {"name": "chat.py", "path": "chat.py", "sha": "f2a1cc0d6c221668ac00a04502420de9b56099e1", "size": 2663, "url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/contents/chat.py?ref=8027c2350972c6d47adc3aa8d509109f44e14a8b", "html_url": "https://github.com/stepanogil/mcp-sse-demo/blob/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "git_url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/git/blobs/f2a1cc0d6c221668ac00a04502420de9b56099e1", "download_url": "https://raw.githubusercontent.com/stepanogil/mcp-sse-demo/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "type": "file", "content": "aW1wb3J0IG5lc3RfYXN5bmNpbwppbXBvcnQganNvbgppbXBvcnQgb3MKZnJv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\nZXNzYWdlLmNvbnRlbnQKICAgICAgICAKICAgICM... [truncated] | service=scanner
2025-08-13 19:45:53 - services.scanner - DEBUG - GitHub search result: {"name": "ai.py", "path": "ai.py", "sha": "7ab937b5c5e13a923ff539867acfb7c69f37e529", "size": 1296, "url": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "html_url": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "git_url": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "download_url": "https://raw.githubusercontent.com/M507/RamiGPT/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "type": "file", "content": "ZnJvbSBkb3RlbnYgaW1wb3J0IGxvYWRfZG90ZW52CmltcG9ydCBvcwpmcm9t\nIG9wZW5haSBpbXBvcnQgT3BlbkFJCgpjbGFzcyBPcGVuQUlDbGllbnQ6CiAg\nICBkZWYgX19pbml0X18oc2VsZik6CiAgICAgICAgIyBMb2FkIGVudmlyb25t\nZW50IHZhcmlhYmxlcwogICAgICAgIGxvYWRfZG90ZW52KCkKICAgICAgICBz\nZWxmLmNsaWVudCA9IE9wZW5BSShhcGlfa2V5PW9zLmdldGVudigiT1BFTkFJ\nX0FQSV9LRVkiKSkKICAgIAogICAgZGVmIGNyZWF0ZV9jb21wbGV0aW9uKHNl\nbGYsIG1lc3NhZ2VzKToKICAgICAgICAiIiIKICAgICAgICBDcmVhdGUgYSB0\nZXh0IGNvbXBsZXRpb24gdXNpbmcgT3BlbkFJIEFQSS4KCiAgICAgICAgQXJn\nczoKICAgICAgICBtZXNzYWdlcyAobGlzdCk6IEEgbGlzdCBvZiBkaWN0aW9u\nYXJpZXMgZGVmaW5pbmcgdGhlIGludGVyYWN0aW9uIGhpc3RvcnksCiAgICAg\nICAgICAgICAgICAgICAgICAgICB3aGVyZSBlYWNoIGRpY3Rpb25hcnkgY29u\ndGFpbnMgJ3JvbGUnIGFuZCAnY29udGVudCcuCgogICAgICAgIFJldHVybnM6\nCiAgICAgICAgc3RyOiBUaGUgY29udGVudCBvZiB0aGUgcmVzcG9uc2UgbWVz\nc2FnZS4KICAgICAgICAiIiIKICAgICAgICBjb21wbGV0aW9uID0gc2VsZi5j\nbGllbnQuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoCiAgICAgICAgICAgIG1v\nZGVsPSJncHQtMy41LXR1cmJvIiwKICAgICAgICAgICAgbWVzc2FnZXM9bWVz\nc2FnZXMKICAgICAgICApCiAgICAgICAgcmV0dXJuIGNvbXBsZXRpb24uY2hv\naWNlc1swXS5tZXNzYWdlLmNvbnRlbnQuc3RyaXAoKQoKZGVmIGdldF9hbnN3\nZXIoY2xpZW50LCBzeXN0ZW0sIHByb21wdCk6CiAgICBtZXNzYWdlcyA9IFsK\nICAgICAgICB7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQiOiBzeXN0ZW19\nLAogICAgICAgIHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBwcm9tcHR9\nCiAgICBdCiAgICByZXNwb25zZSA9IGNsaWVudC5jcmVhdGVfY29tcGxldGlv\nbihtZXNzYWdlcykKICAgIHJldHVybiByZXNwb25zZQoKIyBVc2FnZQppZiBf\nX25hbWVfXyA9PSAiX19tYWluX18iOgogICAgY2xpZW50ID0gT3BlbkFJQ2xp\nZW50KCkKICAgIG1lc3NhZ2VzID0gWwogICAgICAgIHsicm9sZSI6ICJzeXN0\nZW0iLCAiY29udGVudCI6ICJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQu\nIn0sCiAgICAgICAgeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6ICJIZWxs\nbyEifQogICAgXQogICAgcmVzcG9uc2UgPSBjbGllbnQuY3JlYXRlX2NvbXBs\nZXRpb24obWVzc2FnZXMpCiAgICBwcmludChyZXNwb25zZSkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "git": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "html": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py"}} | service=scanner
2025-08-13 19:45:53 - services.scanner - DEBUG - GitHub search result: {"name": "ml.py", "path": "dev/legacy_routing_agent/ml.py", "sha": "cdfa4807c49ac31a19303e7fa741c7b8c0163138", "size": 2536, "url": "https://api.github.com/repos/kingjulio8238/Memary/contents/dev/legacy_routing_agent/ml.py?ref=b2331a2c0844d66f69acd607b9e4dbaba56552c1", "html_url": "https://github.com/kingjulio8238/Memary/blob/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "git_url": "https://api.github.com/repos/kingjulio8238/Memary/git/blobs/cdfa4807c49ac31a19303e7fa741c7b8c0163138", "download_url": "https://raw.githubusercontent.com/kingjulio8238/Memary/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "type": "file", "content": "ZnJvbSBvcGVuYWkgaW1wb3J0IE9wZW5BSQppbXBvcnQgb3MKaW1wb3J0IHJl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\ncnNvbiBhc2tpbmcgYWJvdXQ... [truncated] | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "ui.py", "path": "ui.py", "sha": "d7e486b8152ef4029627f54b95a7423693e7017a", "size": 1685, "url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "html_url": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "git_url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "download_url": "https://raw.githubusercontent.com/MG-Cafe/ChatGPT-Tabular-Data/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "type": "file", "content": "DQppbXBvcnQgb3MNCg0Kb3MuZW52aXJvblsnT1BFTkFJX0FQSV9LRVknXSA9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\ncA0Kcm9vdC5tYWlubG9vcCgpDQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "git": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "html": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py"}} | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "gpt.py", "path": "gpt.py", "sha": "7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "size": 1437, "url": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "html_url": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "git_url": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "download_url": "https://raw.githubusercontent.com/Reikanod/tinderHelp/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "type": "file", "content": "aW1wb3J0IG9wZW5haQpmcm9tIG9wZW5haSBpbXBvcnQgT3BlbkFJCmltcG9y\ndCBodHRweCBhcyBodHRweAoKY2xhc3MgQ2hhdEdwdFNlcnZpY2U6CiAgICBj\nbGllbnQ6IE9wZW5BSSA9IE5vbmUKICAgIG1lc3NhZ2VfbGlzdDogbGlzdCA9\nIE5vbmUKCiAgICBkZWYgX19pbml0X18oc2VsZiwgdG9rZW4pOgogICAgICAg\nIHRva2VuID0gInNrLXByb2otIit0b2tlbls6MzotMV0gaWYgdG9rZW4uc3Rh\ncnRzd2l0aCgnZ3B0OicpIGVsc2UgdG9rZW4KICAgICAgICBzZWxmLmNsaWVu\ndCA9IG9wZW5haS5PcGVuQUkoaHR0cF9jbGllbnQ9aHR0cHguQ2xpZW50KHBy\nb3hpZXM9Imh0dHA6Ly8xOC4xOTkuMTgzLjc3OjQ5MjMyIiksIGFwaV9rZXk9\ndG9rZW4pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QgPSBbXQoKICAgIGRl\nZiBzZW5kX21lc3NhZ2VfbGlzdChzZWxmKSAtPiBzdHI6CiAgICAgICAgY29t\ncGxldGlvbiA9IHNlbGYuY2xpZW50LmNoYXQuY29tcGxldGlvbnMuY3JlYXRl\nKAogICAgICAgICAgICBtb2RlbD0iZ3B0LTMuNS10dXJibyIsICAjIGdwdC00\nbywgIGdwdC00LXR1cmJvLCAgICBncHQtMy41LXR1cmJvCiAgICAgICAgICAg\nIG1lc3NhZ2VzPXNlbGYubWVzc2FnZV9saXN0LAogICAgICAgICAgICBtYXhf\ndG9rZW5zPTMwMDAsCiAgICAgICAgICAgIHRlbXBlcmF0dXJlPTAuOQogICAg\nICAgICkKICAgICAgICBtZXNzYWdlID0gY29tcGxldGlvbi5jaG9pY2VzWzBd\nLm1lc3NhZ2UKICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5hcHBlbmQobWVz\nc2FnZSkKICAgICAgICByZXR1cm4gbWVzc2FnZS5jb250ZW50CgogICAgZGVm\nIHNldF9wcm9tcHQoc2VsZiwgcHJvbXB0X3RleHQ6IHN0cikgLT4gTm9uZToK\nICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5jbGVhcigpCiAgICAgICAgc2Vs\nZi5tZXNzYWdlX2xpc3QuYXBwZW5kKHsicm9sZSI6ICJzeXN0ZW0iLCAiY29u\ndGVudCI6IHByb21wdF90ZXh0fSkKCiAgICBkZWYgYWRkX21lc3NhZ2Uoc2Vs\nZiwgbWVzc2FnZV90ZXh0OiBzdHIpIC0+IHN0cjoKICAgICAgICBzZWxmLm1l\nc3NhZ2VfbGlzdC5hcHBlbmQoeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6\nIG1lc3NhZ2VfdGV4dH0pCiAgICAgICAgcmV0dXJuIHNlbGYuc2VuZF9tZXNz\nYWdlX2xpc3QoKQoKICAgIGRlZiBzZW5kX3F1ZXN0aW9uKHNlbGYsIHByb21w\ndF90ZXh0OiBzdHIsIG1lc3NhZ2VfdGV4dDogc3RyKSAtPiBzdHI6CiAgICAg\nICAgc2VsZi5tZXNzYWdlX2xpc3QuY2xlYXIoKQogICAgICAgIHNlbGYubWVz\nc2FnZV9saXN0LmFwcGVuZCh7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQi\nOiBwcm9tcHRfdGV4dH0pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QuYXBw\nZW5kKHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBtZXNzYWdlX3RleHR9\nKQogICAgICAgIHJldHVybiBzZWxmLnNlbmRfbWVzc2FnZV9saXN0KCkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "git": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "html": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py"}} | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "main.py", "path": "scripts/main.py", "sha": "5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "size": 2284, "url": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "html_url": "https://github.com/mckaywrigley/naval-gpt/blob/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "git_url": "https://api.github.com/repos/mckaywrigley/naval-gpt/git/blobs/5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "download_url": "https://raw.githubusercontent.com/mckaywrigley/naval-gpt/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "type": "file", "content": "aW1wb3J0IG9zCmltcG9ydCBvcGVuYWkKZnJvbSBweWR1YiBpbXBvcnQgQXVk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\nbmVyYXRlX3RyYW5zY3JpcHQoKQpjcmVhdGVfanNvbigpCg==\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "git": "https://api.github.com/repos/mck... [truncated] | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "op.py", "path": "op.py", "sha": "6cad03380c73bbfdf93a17bdaba308824d719cfb", "size": 1289, "url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "html_url": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "git_url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "download_url": "https://raw.githubusercontent.com/NemroNeno/LinkedIn_messaging_agent/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "type": "file", "content": "IyBpbnN0YWxsIGRlcGVuZGVuY2llcyBpZiBuZWVkZWQ6CiMgcGlwIGluc3Rh\nbGwgbGFuZ2NoYWluIG9wZW5haQoKZnJvbSBsYW5nY2hhaW4gaW1wb3J0IE9w\nZW5BSQpmcm9tIGxhbmdjaGFpbi5wcm9tcHRzIGltcG9ydCBQcm9tcHRUZW1w\nbGF0ZQpmcm9tIGxhbmdjaGFpbi5jaGFpbnMgaW1wb3J0IExMTUNoYWluCmlt\ncG9ydCBvcwpmcm9tIGxhbmdjaGFpbl9vcGVuYWkgaW1wb3J0IENoYXRPcGVu\nQUkKCiMgIyBTZXQgeW91ciBBUEkga2V5CiMgb3MuZW52aXJvblsiT1BFTkFJ\nX0FQSV9LRVkiXSA9ICJZT1VSX09QRU5BSV9BUElfS0VZIgojc2stcHJvai0t\nRzVMb1ZUZWh1TmY3dkVBTkVVbEFsa0lZNGtjYWh6MVVDdlowVHNHQ05iS3RZ\nWTdrUmFGdDRPRlBDZGtzb1FNdVZLakFJVUZMWFQzQmxia0ZKWDBmNTUtM2RG\nT3h5RHJjWFkwLWZCUk95ZEpERXhBMzlpaUpodXpEUEZqekFIQzZfUVVfNTNM\ncGxtc0Z4OUJNQm5kSHJxUmYySUEKIyAjIEluaXRpYWxpemUgdGhlIE9wZW5B\nSSBtb2RlbCAoZS5nLiwgR1BULTMuNSkKIyBsbG0gPSBPcGVuQUkobW9kZWxf\nbmFtZT0iZ3B0LTMuNS10dXJibyIsIHRlbXBlcmF0dXJlPTAuNyxhcGlfa2V5\nPSJnc2tfSDdYUkxwd1ZNd2ZITkVFelM1dElXR2R5YjNGWVdjbVRFTE1YaXh1\nWHNndndYR3ZiUEdldyIpCgojIDIuIEluaXRpYWxpemUgQ2hhdE9wZW5BSSBt\nb2RlbApsbG0gPSBDaGF0T3BlbkFJKAogICAgbW9kZWw9ImdwdC0zLjUtdHVy\nYm8iLAogICAgdGVtcGVyYXR1cmU9MC43LAogICAgbWF4X3Rva2Vucz0xNTAs\nCiAgICBhcGlfa2V5PSJzay1wcm9qLVBESDVVQzA3d0hIVDFNY1hhVjRzc0pm\nb0p5WElBYU1QWTB3Y3l0bzdHOG1JZ2l6c0tDSjY2QnZvOXp4aFJqVmFHTk5G\nNENuQWZ3VDNCbGJrRkp2Z04xTGpDeUcwSVVaY2xyWk1GaTlJZ0x1SEVzdkJV\nRXFEUHliSVI2V3JDdEwxWFdrdGM0bldKSmdueWVXUmVKZHE0WGVNUmNrQSIK\nICAgIAopCgojIDMuIFByZXBhcmUgYSBwcm9tcHQgdGVtcGxhdGUKdGVtcGxh\ndGUgPSBQcm9tcHRUZW1wbGF0ZSgKICAgIGlucHV0X3ZhcmlhYmxlcz1bIm5h\nbWUiXSwKICAgIHRlbXBsYXRlPSJXcml0ZSBhIGZyaWVuZGx5IGdyZWV0aW5n\nIHRvIHtuYW1lfS4iCikKCiMgNC4gQ3JlYXRlIHRoZSBMTE1DaGFpbgpjaGFp\nbiA9IExMTUNoYWluKHByb21wdD10ZW1wbGF0ZSwgbGxtPWxsbSkKCiMgNS4g\nUnVuIHRoZSBjaGFpbgppZiBfX25hbWVfXyA9PSAiX19tYWluX18iOgogICAg\nbmFtZSA9ICJBbGljZSIKICAgIHJlc3VsdCA9IGNoYWluLmludm9rZSh7Im5h\nbWUiOiBuYW1lfSkKICAgIHByaW50KHJlc3VsdCk=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "git": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "html": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py"}} | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "s3.py", "path": "AI_generate/s3.py", "sha": "8ca03454c32329775527097717af74c6cb489dbf", "size": 1595, "url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "html_url": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "git_url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "download_url": "https://raw.githubusercontent.com/nogibjj/Detecting-AI-Generated-Fake-Images/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "type": "file", "content": "aW1wb3J0IGJvdG8zCmltcG9ydCBpbwpmcm9tIFBJTCBpbXBvcnQgSW1hZ2UK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\nZShpbnB1dF9pbWFnZV9wYXRoKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "git": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "html": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py"}} | service=scanner
2025-08-13 19:45:54 - services.scanner - DEBUG - GitHub search result: {"name": "app.py", "path": "app.py", "sha": "ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "size": 1673, "url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "html_url": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "git_url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "download_url": "https://raw.githubusercontent.com/mateo-velez/youtube-video-summarizer/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "type": "file", "content": "aW1wb3J0IHN0cmVhbWxpdCBhcyBzdApmcm9tIHV0aWxzIGltcG9ydCAqCgoK\nIyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCndpdGggc3Qu\nc2lkZWJhcjoKICAgIGFwaV9rZXkgPSBzdC50ZXh0X2lucHV0KAogICAgICAg\nIGxhYmVsPSJPUEVOQUkgQVBJIEtFWSIsCiAgICAgICAgcGxhY2Vob2xkZXI9\nInNrLXByb2otKioqIiwKICAgICAgICB0eXBlPSJwYXNzd29yZCIsCiAgICAp\nCiAgICBtZXRob2QgPSBzdC5zZWxlY3Rib3goCiAgICAgICAgbGFiZWw9IlN1\nbW1hcml6aW5nIG1ldGhvZCIsIG9wdGlvbnM9c3VtbWFyaXppbmdfcHJvbXB0\ncy5rZXlzKCkKICAgICkKCiMgLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0t\nLS0tLS0tLQp2aWRlb191cmwgPSBzdC50ZXh0X2lucHV0KAogICAgbGFiZWw9\nIkxpbmsiLCBwbGFjZWhvbGRlcj0iaHR0cHM6Ly93d3cueW91dHViZS5jb20v\nd2F0Y2g/dj1kUXc0dzlXZ1hjUSIKKQoKCmlmIHZpZGVvX3VybCAhPSAiIiBh\nbmQgYXBpX2tleSAhPSAiIjoKICAgICMgR2V0dGluZyBZb3V0dWJlIEluZm8K\nICAgIGluZm8gPSBnZXRfdmlkZW9faW5mbyh2aWRlb191cmwpCgogICAgIyBT\nZWxlY3RpbmcgYmVzdCBsYW5nCiAgICBzdWJzID0ge30KICAgIGZvciBsIGlu\nIGluZm9bInN1YnRpdGxlcyJdLmtleXMoKToKICAgICAgICBpZiBsLnN0YXJ0\nc3dpdGgoImVuIikgb3IgbC5zdGFydHN3aXRoKCJlcyIpOgogICAgICAgICAg\nICBmb3Igc3ViIGluIGluZm9bInN1YnRpdGxlcyJdW2xdOgogICAgICAgICAg\nICAgICAgaWYgc3ViWyJleHQiXSA9PSAidnR0IjoKICAgICAgICAgICAgICAg\nICAgICBzdWJzW2xdID0gc3ViCiAgICBsYW5nID0gZ2V0X2hpZ2hlc3RfcHJp\nb3JpdHkoc3Vicy5rZXlzKCkpCgogICAgIyBTaG93aW5nIHZpZGVvIGluZm8K\nICAgIHN0LndyaXRlKGYiIyMjIHtpbmZvWyd0aXRsZSddfSIpCiAgICBzdC53\ncml0ZSgKICAgICAgICBmIioqRHVyYXRpb246Kioge3NlY29uZHNfdG9fdGlt\nZShpbmZvWydkdXJhdGlvbiddKX0gKipWaWV3czoqKiB7bnVtZXJpY190b19o\ndW1hbihpbmZvWyd2aWV3cyddKX0iCiAgICApCiAgICBzdC5pbWFnZShpbmZv\nWyJ0aHVtYm5haWwiXSkKCiAgICAjIFByZXBhcmluZyBwcm9tcHQKICAgIHBy\nb21wdCA9IHByZXBhcmVfcHJvbXB0KG1ldGhvZD1tZXRob2QpCgogICAgIyBX\ncml0aW5nIHN5bnRlc2l6ZWQgdGV4dC4KICAgIHN0LndyaXRlKCItLS0iKQog\nICAgc3Qud3JpdGUoc3luIDo9IHN1bW1hcml6ZShwcm9tcHQsIHRleHQgOj0g\nZ2V0X3N1YnMoc3Vic1tsYW5nXSksIGFwaV9rZXk9YXBpX2tleSkpCgogICAg\nIyBQcmVwYXJpbmcgZG93bmxvYWQgYm90dG9ucy4KICAgIHN0LndyaXRlKCIt\nLS0iKQogICAgYzEsIGMyID0gc3QuY29sdW1ucygyKQogICAgYzEuZG93bmxv\nYWRfYnV0dG9uKAogICAgICAgIGxhYmVsPSJSYXciLAogICAgICAgIGRhdGE9\ndGV4dCwKICAgICAgICBmaWxlX25hbWU9ZiJ7aW5mb1sndGl0bGUnXX0udHh0\nIiwKICAgICAgICB1c2VfY29udGFpbmVyX3dpZHRoPVRydWUsCiAgICApCiAg\nICBjMi5kb3dubG9hZF9idXR0b24oCiAgICAgICAgbGFiZWw9IlRleHQiLAog\nICAgICAgIGRhdGE9c3luLAogICAgICAgIGZpbGVfbmFtZT1mIntpbmZvWyd0\naXRsZSddfS5tZCIsCiAgICAgICAgdXNlX2NvbnRhaW5lcl93aWR0aD1UcnVl\nLAogICAgKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "git": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "html": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py"}} | service=scanner
2025-08-13 19:45:55 - services.scanner - DEBUG - GitHub search result: {"name": "web.py", "path": "web.py", "sha": "130ec701c9a15dfad9b293ff6f2e7278ae319571", "size": 2715, "url": "https://api.github.com/repos/aashique1915005/web-scraping/contents/web.py?ref=552d0a044c14f61a7dae9dacc2818bb9fe434bd1", "html_url": "https://github.com/aashique1915005/web-scraping/blob/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "git_url": "https://api.github.com/repos/aashique1915005/web-scraping/git/blobs/130ec701c9a15dfad9b293ff6f2e7278ae319571", "download_url": "https://raw.githubusercontent.com/aashique1915005/web-scraping/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "type": "file", "content": "aW1wb3J0IG9zDQppbXBvcnQgcmVxdWVzdHMNCmZyb20gZG90ZW52IGltcG9y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\nICAgbW9kZWwgPSAiZ3B0LTRv... [truncated] | service=scanner
2025-08-13 19:46:28 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 100, 'found_keys': 7, 'saved_keys': 5} | service=scanner
2025-08-13 19:46:31 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 100, 'found_keys': 74, 'saved_keys': 0} | service=scanner
2025-08-13 19:46:31 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 10:50:05 - root - INFO - Logging configuration initialized
2025-08-14 10:50:05 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 10:50:05 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 10:50:05 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 10:50:05 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 10:50:05 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 10:50:05 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 10:50:05 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 10:50:05 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 10:50:06 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 02:51:06 | service=scanner | query_id=5
2025-08-14 10:50:06 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 02:51:06 | service=scanner | query_id=1
2025-08-14 10:50:07 - services.scanner - DEBUG - GitHub search result: {"name": "ai.py", "path": "ai.py", "sha": "7ab937b5c5e13a923ff539867acfb7c69f37e529", "size": 1296, "url": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "html_url": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "git_url": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "download_url": "https://raw.githubusercontent.com/M507/RamiGPT/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "type": "file", "content": "ZnJvbSBkb3RlbnYgaW1wb3J0IGxvYWRfZG90ZW52CmltcG9ydCBvcwpmcm9t\nIG9wZW5haSBpbXBvcnQgT3BlbkFJCgpjbGFzcyBPcGVuQUlDbGllbnQ6CiAg\nICBkZWYgX19pbml0X18oc2VsZik6CiAgICAgICAgIyBMb2FkIGVudmlyb25t\nZW50IHZhcmlhYmxlcwogICAgICAgIGxvYWRfZG90ZW52KCkKICAgICAgICBz\nZWxmLmNsaWVudCA9IE9wZW5BSShhcGlfa2V5PW9zLmdldGVudigiT1BFTkFJ\nX0FQSV9LRVkiKSkKICAgIAogICAgZGVmIGNyZWF0ZV9jb21wbGV0aW9uKHNl\nbGYsIG1lc3NhZ2VzKToKICAgICAgICAiIiIKICAgICAgICBDcmVhdGUgYSB0\nZXh0IGNvbXBsZXRpb24gdXNpbmcgT3BlbkFJIEFQSS4KCiAgICAgICAgQXJn\nczoKICAgICAgICBtZXNzYWdlcyAobGlzdCk6IEEgbGlzdCBvZiBkaWN0aW9u\nYXJpZXMgZGVmaW5pbmcgdGhlIGludGVyYWN0aW9uIGhpc3RvcnksCiAgICAg\nICAgICAgICAgICAgICAgICAgICB3aGVyZSBlYWNoIGRpY3Rpb25hcnkgY29u\ndGFpbnMgJ3JvbGUnIGFuZCAnY29udGVudCcuCgogICAgICAgIFJldHVybnM6\nCiAgICAgICAgc3RyOiBUaGUgY29udGVudCBvZiB0aGUgcmVzcG9uc2UgbWVz\nc2FnZS4KICAgICAgICAiIiIKICAgICAgICBjb21wbGV0aW9uID0gc2VsZi5j\nbGllbnQuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoCiAgICAgICAgICAgIG1v\nZGVsPSJncHQtMy41LXR1cmJvIiwKICAgICAgICAgICAgbWVzc2FnZXM9bWVz\nc2FnZXMKICAgICAgICApCiAgICAgICAgcmV0dXJuIGNvbXBsZXRpb24uY2hv\naWNlc1swXS5tZXNzYWdlLmNvbnRlbnQuc3RyaXAoKQoKZGVmIGdldF9hbnN3\nZXIoY2xpZW50LCBzeXN0ZW0sIHByb21wdCk6CiAgICBtZXNzYWdlcyA9IFsK\nICAgICAgICB7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQiOiBzeXN0ZW19\nLAogICAgICAgIHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBwcm9tcHR9\nCiAgICBdCiAgICByZXNwb25zZSA9IGNsaWVudC5jcmVhdGVfY29tcGxldGlv\nbihtZXNzYWdlcykKICAgIHJldHVybiByZXNwb25zZQoKIyBVc2FnZQppZiBf\nX25hbWVfXyA9PSAiX19tYWluX18iOgogICAgY2xpZW50ID0gT3BlbkFJQ2xp\nZW50KCkKICAgIG1lc3NhZ2VzID0gWwogICAgICAgIHsicm9sZSI6ICJzeXN0\nZW0iLCAiY29udGVudCI6ICJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQu\nIn0sCiAgICAgICAgeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6ICJIZWxs\nbyEifQogICAgXQogICAgcmVzcG9uc2UgPSBjbGllbnQuY3JlYXRlX2NvbXBs\nZXRpb24obWVzc2FnZXMpCiAgICBwcmludChyZXNwb25zZSkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "git": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "html": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py"}} | service=scanner
2025-08-14 10:50:07 - services.scanner - DEBUG - GitHub search result: {"name": "chat.py", "path": "chat.py", "sha": "f2a1cc0d6c221668ac00a04502420de9b56099e1", "size": 2663, "url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/contents/chat.py?ref=8027c2350972c6d47adc3aa8d509109f44e14a8b", "html_url": "https://github.com/stepanogil/mcp-sse-demo/blob/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "git_url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/git/blobs/f2a1cc0d6c221668ac00a04502420de9b56099e1", "download_url": "https://raw.githubusercontent.com/stepanogil/mcp-sse-demo/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "type": "file", "content": "aW1wb3J0IG5lc3RfYXN5bmNpbwppbXBvcnQganNvbgppbXBvcnQgb3MKZnJv\nbSBvcGVuYWkgaW1wb3J0IEFzeW5jT3BlbkFJCmZyb20gbWNwX2NsaWVudHMu\nemFwaWVyX21jcF9jbGllbnQgaW1wb3J0IEdtYWlsTUNQQ2xpZW50CmZyb20g\nZG90ZW52IGltcG9ydCBsb2FkX2RvdGVudgoKbG9hZF9kb3RlbnYoKQoKIyBs\nb2FkIGNyZWRlbnRpYWxzIGZyb20gLmVudiBmaWxlCkFQSV9LRVkgPSBvcy5n\nZXRlbnYoIk9QRU5BSV9BUElfS0VZIikgIyAnc2stcHJvai0uLi4nClpBUElF\nUl9VUkwgPSBvcy5nZXRlbnYoIlpBUElFUl9VUkwiKSAjICdodHRwczovL2Fj\ndGlvbnMuemFwaWVyLmNvbS9tY3AveW91LXNlY3JldC1rZXkvc3NlJwoKY2xp\nZW50ID0gQXN5bmNPcGVuQUkoYXBpX2tleT1BUElfS0VZKQoKIyBhbGxvd3Mg\nYXN5bmMgZnVuY3Rpb25zIHRvIHJ1biBpbiBqdXB5dGVyIG5vdGVib29rCm5l\nc3RfYXN5bmNpby5hcHBseSgpCgojIGluaXRpYWxpemUgdGhlIEdtYWlsIE1D\nUCBjbGllbnQKZ21haWxfbWNwX2NsaWVudCA9IEdtYWlsTUNQQ2xpZW50KCkK\nCiMgY2hhdCBmdW5jdGlvbgphc3luYyBkZWYgY2hhdCh1c2VyX2lucHV0KToK\nICAgICIiIgogICAgUHJvY2Vzc2VzIHVzZXIgaW5wdXQgdGhyb3VnaCBhIHR3\nby1zdGVwIExMTSBpbnRlcmFjdGlvbiB3aXRoIHRvb2wgaW50ZWdyYXRpb24u\nCgogICAgVGhpcyBmdW5jdGlvbiBwZXJmb3JtcyB0aGUgZm9sbG93aW5nIHN0\nZXBzOgogICAgMS4gQ29ubmVjdHMgdG8gR21haWwgTUNQIHNlcnZlciBhbmQg\ncmV0cmlldmVzIGF2YWlsYWJsZSB0b29scwogICAgMi4gTWFrZXMgaW5pdGlh\nbCBMTE0gY2FsbCB0byBkZXRlcm1pbmUgd2hpY2ggdG9vbCB0byB1c2UKICAg\nIDMuIEV4ZWN1dGVzIHRoZSBzZWxlY3RlZCB0b29sIHdpdGggcHJvdmlkZWQg\nYXJndW1lbnRzCiAgICA0LiBNYWtlcyBzZWNvbmQgTExNIGNhbGwgdG8gZ2Vu\nZXJhdGUgZmluYWwgcmVzcG9uc2UgYmFzZWQgb24gdG9vbCBvdXRwdXQKCiAg\nICBBcmdzOgogICAgICAgIHVzZXJfaW5wdXQgKHN0cik6IFRoZSBpbnB1dCBt\nZXNzYWdlIGZyb20gdGhlIHVzZXIgdG8gYmUgcHJvY2Vzc2VkCgogICAgUmV0\ndXJuczoKICAgICAgICBzdHI6IFRoZSBmaW5hbCByZXNwb25zZSBtZXNzYWdl\nIGZyb20gdGhlIExMTQoKICAgIFJhaXNlczoKICAgICAgICBOb25lCiAgICAi\nIiIKCiAgICAjIGdldCB0b29scyBmcm9tIFphcGllciBzZXJ2ZXIKICAgIGF3\nYWl0IGdtYWlsX21jcF9jbGllbnQuY29ubmVjdF90b19zZXJ2ZXIoWkFQSUVS\nX1VSTCkKICAgIHRvb2xzID0gYXdhaXQgZ21haWxfbWNwX2NsaWVudC5nZXRf\ndG9vbHMoKSAgICAKCiAgICAjIDFzdCBMTE0gY2FsbCB0byBkZXRlcm1pbmUg\nd2hpY2ggdG9vbCB0byB1c2UKICAgIHJlc3BvbnNlID0gYXdhaXQgY2xpZW50\nLmNoYXQuY29tcGxldGlvbnMuY3JlYXRlKAogICAgICAgIG1vZGVsPSJncHQt\nNG8tbWluaSIsCiAgICAgICAgbWVzc2FnZXM9W3sicm9sZSI6ICJ1c2VyIiwg\nImNvbnRlbnQiOiB1c2VyX2lucHV0fV0sCiAgICAgICAgdG9vbHM9dG9vbHMK\nICAgICkKCiAgICAjIGlmIExMTSBkZWNpZGVzIHRvIHVzZSBhIHRvb2wKICAg\nIGlmIHJlc3BvbnNlLmNob2ljZXNbMF0ubWVzc2FnZS50b29sX2NhbGxzOiAg\nICAgICAgCiAgICAgICAgdG9vbF9uYW1lID0gcmVzcG9uc2UuY2hvaWNlc1sw\nXS5tZXNzYWdlLnRvb2xfY2FsbHNbMF0uZnVuY3Rpb24ubmFtZQogICAgICAg\nIHRvb2xfYXJncyA9IGpzb24ubG9hZHMocmVzcG9uc2UuY2hvaWNlc1swXS5t\nZXNzYWdlLnRvb2xfY2FsbHNbMF0uZnVuY3Rpb24uYXJndW1lbnRzKQogICAg\nICAgIHByaW50KGYiVG9vbCBVc2VkOiB7dG9vbF9uYW1lfSwgQXJndW1lbnRz\nOiB7dG9vbF9hcmdzfSIpCgogICAgICAgICMgZXhlY3V0ZSB0aGUgdG9vbCBj\nYWxsZWQgYnkgdGhlIExMTQogICAgICAgIHRvb2xfcmVzcG9uc2UgPSBhd2Fp\ndCBnbWFpbF9tY3BfY2xpZW50LnNlc3Npb24uY2FsbF90b29sKHRvb2xfbmFt\nZSwgdG9vbF9hcmdzKQogICAgICAgIHRvb2xfcmVzcG9uc2VfdGV4dCA9IHRv\nb2xfcmVzcG9uc2UuY29udGVudFswXS50ZXh0ICAgIAoKICAgICAgICAjIDJu\nZCBMTE0gY2FsbCB0byBkZXRlcm1pbmUgZmluYWwgcmVzcG9uc2UKICAgICAg\nICByZXMgPSBhd2FpdCBjbGllbnQuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUo\nCiAgICAgICAgICAgIG1vZGVsPSJncHQtNG8tbWluaSIsCiAgICAgICAgICAg\nIG1lc3NhZ2VzPVsKICAgICAgICAgICAgICAgIHsicm9sZSI6ICJ1c2VyIiwg\nImNvbnRlbnQiOiB1c2VyX2lucHV0fSwKICAgICAgICAgICAgICAgIHsicm9s\nZSI6ICJmdW5jdGlvbiIsICJuYW1lIjogdG9vbF9uYW1lLCAiY29udGVudCI6\nIHRvb2xfcmVzcG9uc2VfdGV4dH0sCiAgICAgICAgICAgIF0gICAgICAgIAog\nICAgICAgICkKCiAgICAgICAgcmVzcG9uc2UgPSByZXMuY2hvaWNlc1swXS5t\nZXNzYWdlLmNvbnRlbnQKICAgICAgICAKICAgICM... [truncated] | service=scanner
2025-08-14 10:50:07 - services.scanner - DEBUG - GitHub search result: {"name": "ml.py", "path": "dev/legacy_routing_agent/ml.py", "sha": "cdfa4807c49ac31a19303e7fa741c7b8c0163138", "size": 2536, "url": "https://api.github.com/repos/kingjulio8238/Memary/contents/dev/legacy_routing_agent/ml.py?ref=b2331a2c0844d66f69acd607b9e4dbaba56552c1", "html_url": "https://github.com/kingjulio8238/Memary/blob/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "git_url": "https://api.github.com/repos/kingjulio8238/Memary/git/blobs/cdfa4807c49ac31a19303e7fa741c7b8c0163138", "download_url": "https://raw.githubusercontent.com/kingjulio8238/Memary/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "type": "file", "content": "ZnJvbSBvcGVuYWkgaW1wb3J0IE9wZW5BSQppbXBvcnQgb3MKaW1wb3J0IHJl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\ncnNvbiBhc2tpbmcgYWJvdXQ... [truncated] | service=scanner
2025-08-14 10:50:07 - services.scanner - DEBUG - GitHub search result: {"name": "gpt.py", "path": "gpt.py", "sha": "7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "size": 1437, "url": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "html_url": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "git_url": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "download_url": "https://raw.githubusercontent.com/Reikanod/tinderHelp/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "type": "file", "content": "aW1wb3J0IG9wZW5haQpmcm9tIG9wZW5haSBpbXBvcnQgT3BlbkFJCmltcG9y\ndCBodHRweCBhcyBodHRweAoKY2xhc3MgQ2hhdEdwdFNlcnZpY2U6CiAgICBj\nbGllbnQ6IE9wZW5BSSA9IE5vbmUKICAgIG1lc3NhZ2VfbGlzdDogbGlzdCA9\nIE5vbmUKCiAgICBkZWYgX19pbml0X18oc2VsZiwgdG9rZW4pOgogICAgICAg\nIHRva2VuID0gInNrLXByb2otIit0b2tlbls6MzotMV0gaWYgdG9rZW4uc3Rh\ncnRzd2l0aCgnZ3B0OicpIGVsc2UgdG9rZW4KICAgICAgICBzZWxmLmNsaWVu\ndCA9IG9wZW5haS5PcGVuQUkoaHR0cF9jbGllbnQ9aHR0cHguQ2xpZW50KHBy\nb3hpZXM9Imh0dHA6Ly8xOC4xOTkuMTgzLjc3OjQ5MjMyIiksIGFwaV9rZXk9\ndG9rZW4pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QgPSBbXQoKICAgIGRl\nZiBzZW5kX21lc3NhZ2VfbGlzdChzZWxmKSAtPiBzdHI6CiAgICAgICAgY29t\ncGxldGlvbiA9IHNlbGYuY2xpZW50LmNoYXQuY29tcGxldGlvbnMuY3JlYXRl\nKAogICAgICAgICAgICBtb2RlbD0iZ3B0LTMuNS10dXJibyIsICAjIGdwdC00\nbywgIGdwdC00LXR1cmJvLCAgICBncHQtMy41LXR1cmJvCiAgICAgICAgICAg\nIG1lc3NhZ2VzPXNlbGYubWVzc2FnZV9saXN0LAogICAgICAgICAgICBtYXhf\ndG9rZW5zPTMwMDAsCiAgICAgICAgICAgIHRlbXBlcmF0dXJlPTAuOQogICAg\nICAgICkKICAgICAgICBtZXNzYWdlID0gY29tcGxldGlvbi5jaG9pY2VzWzBd\nLm1lc3NhZ2UKICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5hcHBlbmQobWVz\nc2FnZSkKICAgICAgICByZXR1cm4gbWVzc2FnZS5jb250ZW50CgogICAgZGVm\nIHNldF9wcm9tcHQoc2VsZiwgcHJvbXB0X3RleHQ6IHN0cikgLT4gTm9uZToK\nICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5jbGVhcigpCiAgICAgICAgc2Vs\nZi5tZXNzYWdlX2xpc3QuYXBwZW5kKHsicm9sZSI6ICJzeXN0ZW0iLCAiY29u\ndGVudCI6IHByb21wdF90ZXh0fSkKCiAgICBkZWYgYWRkX21lc3NhZ2Uoc2Vs\nZiwgbWVzc2FnZV90ZXh0OiBzdHIpIC0+IHN0cjoKICAgICAgICBzZWxmLm1l\nc3NhZ2VfbGlzdC5hcHBlbmQoeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6\nIG1lc3NhZ2VfdGV4dH0pCiAgICAgICAgcmV0dXJuIHNlbGYuc2VuZF9tZXNz\nYWdlX2xpc3QoKQoKICAgIGRlZiBzZW5kX3F1ZXN0aW9uKHNlbGYsIHByb21w\ndF90ZXh0OiBzdHIsIG1lc3NhZ2VfdGV4dDogc3RyKSAtPiBzdHI6CiAgICAg\nICAgc2VsZi5tZXNzYWdlX2xpc3QuY2xlYXIoKQogICAgICAgIHNlbGYubWVz\nc2FnZV9saXN0LmFwcGVuZCh7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQi\nOiBwcm9tcHRfdGV4dH0pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QuYXBw\nZW5kKHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBtZXNzYWdlX3RleHR9\nKQogICAgICAgIHJldHVybiBzZWxmLnNlbmRfbWVzc2FnZV9saXN0KCkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "git": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "html": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py"}} | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "ui.py", "path": "ui.py", "sha": "d7e486b8152ef4029627f54b95a7423693e7017a", "size": 1685, "url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "html_url": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "git_url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "download_url": "https://raw.githubusercontent.com/MG-Cafe/ChatGPT-Tabular-Data/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "type": "file", "content": "DQppbXBvcnQgb3MNCg0Kb3MuZW52aXJvblsnT1BFTkFJX0FQSV9LRVknXSA9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\ncA0Kcm9vdC5tYWlubG9vcCgpDQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "git": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "html": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py"}} | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "op.py", "path": "op.py", "sha": "6cad03380c73bbfdf93a17bdaba308824d719cfb", "size": 1289, "url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "html_url": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "git_url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "download_url": "https://raw.githubusercontent.com/NemroNeno/LinkedIn_messaging_agent/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "type": "file", "content": "IyBpbnN0YWxsIGRlcGVuZGVuY2llcyBpZiBuZWVkZWQ6CiMgcGlwIGluc3Rh\nbGwgbGFuZ2NoYWluIG9wZW5haQoKZnJvbSBsYW5nY2hhaW4gaW1wb3J0IE9w\nZW5BSQpmcm9tIGxhbmdjaGFpbi5wcm9tcHRzIGltcG9ydCBQcm9tcHRUZW1w\nbGF0ZQpmcm9tIGxhbmdjaGFpbi5jaGFpbnMgaW1wb3J0IExMTUNoYWluCmlt\ncG9ydCBvcwpmcm9tIGxhbmdjaGFpbl9vcGVuYWkgaW1wb3J0IENoYXRPcGVu\nQUkKCiMgIyBTZXQgeW91ciBBUEkga2V5CiMgb3MuZW52aXJvblsiT1BFTkFJ\nX0FQSV9LRVkiXSA9ICJZT1VSX09QRU5BSV9BUElfS0VZIgojc2stcHJvai0t\nRzVMb1ZUZWh1TmY3dkVBTkVVbEFsa0lZNGtjYWh6MVVDdlowVHNHQ05iS3RZ\nWTdrUmFGdDRPRlBDZGtzb1FNdVZLakFJVUZMWFQzQmxia0ZKWDBmNTUtM2RG\nT3h5RHJjWFkwLWZCUk95ZEpERXhBMzlpaUpodXpEUEZqekFIQzZfUVVfNTNM\ncGxtc0Z4OUJNQm5kSHJxUmYySUEKIyAjIEluaXRpYWxpemUgdGhlIE9wZW5B\nSSBtb2RlbCAoZS5nLiwgR1BULTMuNSkKIyBsbG0gPSBPcGVuQUkobW9kZWxf\nbmFtZT0iZ3B0LTMuNS10dXJibyIsIHRlbXBlcmF0dXJlPTAuNyxhcGlfa2V5\nPSJnc2tfSDdYUkxwd1ZNd2ZITkVFelM1dElXR2R5YjNGWVdjbVRFTE1YaXh1\nWHNndndYR3ZiUEdldyIpCgojIDIuIEluaXRpYWxpemUgQ2hhdE9wZW5BSSBt\nb2RlbApsbG0gPSBDaGF0T3BlbkFJKAogICAgbW9kZWw9ImdwdC0zLjUtdHVy\nYm8iLAogICAgdGVtcGVyYXR1cmU9MC43LAogICAgbWF4X3Rva2Vucz0xNTAs\nCiAgICBhcGlfa2V5PSJzay1wcm9qLVBESDVVQzA3d0hIVDFNY1hhVjRzc0pm\nb0p5WElBYU1QWTB3Y3l0bzdHOG1JZ2l6c0tDSjY2QnZvOXp4aFJqVmFHTk5G\nNENuQWZ3VDNCbGJrRkp2Z04xTGpDeUcwSVVaY2xyWk1GaTlJZ0x1SEVzdkJV\nRXFEUHliSVI2V3JDdEwxWFdrdGM0bldKSmdueWVXUmVKZHE0WGVNUmNrQSIK\nICAgIAopCgojIDMuIFByZXBhcmUgYSBwcm9tcHQgdGVtcGxhdGUKdGVtcGxh\ndGUgPSBQcm9tcHRUZW1wbGF0ZSgKICAgIGlucHV0X3ZhcmlhYmxlcz1bIm5h\nbWUiXSwKICAgIHRlbXBsYXRlPSJXcml0ZSBhIGZyaWVuZGx5IGdyZWV0aW5n\nIHRvIHtuYW1lfS4iCikKCiMgNC4gQ3JlYXRlIHRoZSBMTE1DaGFpbgpjaGFp\nbiA9IExMTUNoYWluKHByb21wdD10ZW1wbGF0ZSwgbGxtPWxsbSkKCiMgNS4g\nUnVuIHRoZSBjaGFpbgppZiBfX25hbWVfXyA9PSAiX19tYWluX18iOgogICAg\nbmFtZSA9ICJBbGljZSIKICAgIHJlc3VsdCA9IGNoYWluLmludm9rZSh7Im5h\nbWUiOiBuYW1lfSkKICAgIHByaW50KHJlc3VsdCk=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "git": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "html": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py"}} | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "app.py", "path": "app.py", "sha": "ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "size": 1673, "url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "html_url": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "git_url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "download_url": "https://raw.githubusercontent.com/mateo-velez/youtube-video-summarizer/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "type": "file", "content": "aW1wb3J0IHN0cmVhbWxpdCBhcyBzdApmcm9tIHV0aWxzIGltcG9ydCAqCgoK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\nLAogICAgKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "git": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "html": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py"}} | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "main.py", "path": "scripts/main.py", "sha": "5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "size": 2284, "url": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "html_url": "https://github.com/mckaywrigley/naval-gpt/blob/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "git_url": "https://api.github.com/repos/mckaywrigley/naval-gpt/git/blobs/5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "download_url": "https://raw.githubusercontent.com/mckaywrigley/naval-gpt/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "type": "file", "content": "aW1wb3J0IG9zCmltcG9ydCBvcGVuYWkKZnJvbSBweWR1YiBpbXBvcnQgQXVk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\nbmVyYXRlX3RyYW5zY3JpcHQoKQpjcmVhdGVfanNvbigpCg==\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "git": "https://api.github.com/repos/mck... [truncated] | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "web.py", "path": "web.py", "sha": "130ec701c9a15dfad9b293ff6f2e7278ae319571", "size": 2715, "url": "https://api.github.com/repos/aashique1915005/web-scraping/contents/web.py?ref=552d0a044c14f61a7dae9dacc2818bb9fe434bd1", "html_url": "https://github.com/aashique1915005/web-scraping/blob/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "git_url": "https://api.github.com/repos/aashique1915005/web-scraping/git/blobs/130ec701c9a15dfad9b293ff6f2e7278ae319571", "download_url": "https://raw.githubusercontent.com/aashique1915005/web-scraping/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "type": "file", "content": "aW1wb3J0IG9zDQppbXBvcnQgcmVxdWVzdHMNCmZyb20gZG90ZW52IGltcG9y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\nICAgbW9kZWwgPSAiZ3B0LTRv... [truncated] | service=scanner
2025-08-14 10:50:08 - services.scanner - DEBUG - GitHub search result: {"name": "s3.py", "path": "AI_generate/s3.py", "sha": "8ca03454c32329775527097717af74c6cb489dbf", "size": 1595, "url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "html_url": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "git_url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "download_url": "https://raw.githubusercontent.com/nogibjj/Detecting-AI-Generated-Fake-Images/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "type": "file", "content": "aW1wb3J0IGJvdG8zCmltcG9ydCBpbwpmcm9tIFBJTCBpbXBvcnQgSW1hZ2UK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\nZShpbnB1dF9pbWFnZV9wYXRoKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "git": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "html": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py"}} | service=scanner
2025-08-14 10:50:43 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 100, 'found_keys': 74, 'saved_keys': 0} | service=scanner
2025-08-14 10:50:43 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 100, 'found_keys': 7, 'saved_keys': 0} | service=scanner
2025-08-14 10:50:43 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 14:58:39 - root - INFO - Logging configuration initialized
2025-08-14 14:58:39 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 14:58:39 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 14:58:39 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 14:58:39 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 14:58:39 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 14:58:39 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 14:58:39 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 14:58:39 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 14:58:40 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 06:59:41 | service=scanner | query_id=5
2025-08-14 14:58:40 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 06:59:41 | service=scanner | query_id=1
2025-08-14 14:58:41 - services.scanner - DEBUG - GitHub search result: {"name": "chat.py", "path": "chat.py", "sha": "f2a1cc0d6c221668ac00a04502420de9b56099e1", "size": 2663, "url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/contents/chat.py?ref=8027c2350972c6d47adc3aa8d509109f44e14a8b", "html_url": "https://github.com/stepanogil/mcp-sse-demo/blob/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "git_url": "https://api.github.com/repos/stepanogil/mcp-sse-demo/git/blobs/f2a1cc0d6c221668ac00a04502420de9b56099e1", "download_url": "https://raw.githubusercontent.com/stepanogil/mcp-sse-demo/8027c2350972c6d47adc3aa8d509109f44e14a8b/chat.py", "type": "file", "content": "aW1wb3J0IG5lc3RfYXN5bmNpbwppbXBvcnQganNvbgppbXBvcnQgb3MKZnJv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\nZXNzYWdlLmNvbnRlbnQKICAgICAgICAKICAgICM... [truncated] | service=scanner
2025-08-14 14:58:41 - services.scanner - DEBUG - GitHub search result: {"name": "gpt.py", "path": "gpt.py", "sha": "7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "size": 1437, "url": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "html_url": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "git_url": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "download_url": "https://raw.githubusercontent.com/Reikanod/tinderHelp/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py", "type": "file", "content": "aW1wb3J0IG9wZW5haQpmcm9tIG9wZW5haSBpbXBvcnQgT3BlbkFJCmltcG9y\ndCBodHRweCBhcyBodHRweAoKY2xhc3MgQ2hhdEdwdFNlcnZpY2U6CiAgICBj\nbGllbnQ6IE9wZW5BSSA9IE5vbmUKICAgIG1lc3NhZ2VfbGlzdDogbGlzdCA9\nIE5vbmUKCiAgICBkZWYgX19pbml0X18oc2VsZiwgdG9rZW4pOgogICAgICAg\nIHRva2VuID0gInNrLXByb2otIit0b2tlbls6MzotMV0gaWYgdG9rZW4uc3Rh\ncnRzd2l0aCgnZ3B0OicpIGVsc2UgdG9rZW4KICAgICAgICBzZWxmLmNsaWVu\ndCA9IG9wZW5haS5PcGVuQUkoaHR0cF9jbGllbnQ9aHR0cHguQ2xpZW50KHBy\nb3hpZXM9Imh0dHA6Ly8xOC4xOTkuMTgzLjc3OjQ5MjMyIiksIGFwaV9rZXk9\ndG9rZW4pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QgPSBbXQoKICAgIGRl\nZiBzZW5kX21lc3NhZ2VfbGlzdChzZWxmKSAtPiBzdHI6CiAgICAgICAgY29t\ncGxldGlvbiA9IHNlbGYuY2xpZW50LmNoYXQuY29tcGxldGlvbnMuY3JlYXRl\nKAogICAgICAgICAgICBtb2RlbD0iZ3B0LTMuNS10dXJibyIsICAjIGdwdC00\nbywgIGdwdC00LXR1cmJvLCAgICBncHQtMy41LXR1cmJvCiAgICAgICAgICAg\nIG1lc3NhZ2VzPXNlbGYubWVzc2FnZV9saXN0LAogICAgICAgICAgICBtYXhf\ndG9rZW5zPTMwMDAsCiAgICAgICAgICAgIHRlbXBlcmF0dXJlPTAuOQogICAg\nICAgICkKICAgICAgICBtZXNzYWdlID0gY29tcGxldGlvbi5jaG9pY2VzWzBd\nLm1lc3NhZ2UKICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5hcHBlbmQobWVz\nc2FnZSkKICAgICAgICByZXR1cm4gbWVzc2FnZS5jb250ZW50CgogICAgZGVm\nIHNldF9wcm9tcHQoc2VsZiwgcHJvbXB0X3RleHQ6IHN0cikgLT4gTm9uZToK\nICAgICAgICBzZWxmLm1lc3NhZ2VfbGlzdC5jbGVhcigpCiAgICAgICAgc2Vs\nZi5tZXNzYWdlX2xpc3QuYXBwZW5kKHsicm9sZSI6ICJzeXN0ZW0iLCAiY29u\ndGVudCI6IHByb21wdF90ZXh0fSkKCiAgICBkZWYgYWRkX21lc3NhZ2Uoc2Vs\nZiwgbWVzc2FnZV90ZXh0OiBzdHIpIC0+IHN0cjoKICAgICAgICBzZWxmLm1l\nc3NhZ2VfbGlzdC5hcHBlbmQoeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6\nIG1lc3NhZ2VfdGV4dH0pCiAgICAgICAgcmV0dXJuIHNlbGYuc2VuZF9tZXNz\nYWdlX2xpc3QoKQoKICAgIGRlZiBzZW5kX3F1ZXN0aW9uKHNlbGYsIHByb21w\ndF90ZXh0OiBzdHIsIG1lc3NhZ2VfdGV4dDogc3RyKSAtPiBzdHI6CiAgICAg\nICAgc2VsZi5tZXNzYWdlX2xpc3QuY2xlYXIoKQogICAgICAgIHNlbGYubWVz\nc2FnZV9saXN0LmFwcGVuZCh7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQi\nOiBwcm9tcHRfdGV4dH0pCiAgICAgICAgc2VsZi5tZXNzYWdlX2xpc3QuYXBw\nZW5kKHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBtZXNzYWdlX3RleHR9\nKQogICAgICAgIHJldHVybiBzZWxmLnNlbmRfbWVzc2FnZV9saXN0KCkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/Reikanod/tinderHelp/contents/gpt.py?ref=d84ba6eaa8ccaf1ef25216764d23fd611429016c", "git": "https://api.github.com/repos/Reikanod/tinderHelp/git/blobs/7281d1e829fa8d47ce5785c0fc60b123792ab4f8", "html": "https://github.com/Reikanod/tinderHelp/blob/d84ba6eaa8ccaf1ef25216764d23fd611429016c/gpt.py"}} | service=scanner
2025-08-14 14:58:41 - services.scanner - DEBUG - GitHub search result: {"name": "ai.py", "path": "ai.py", "sha": "7ab937b5c5e13a923ff539867acfb7c69f37e529", "size": 1296, "url": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "html_url": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "git_url": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "download_url": "https://raw.githubusercontent.com/M507/RamiGPT/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py", "type": "file", "content": "ZnJvbSBkb3RlbnYgaW1wb3J0IGxvYWRfZG90ZW52CmltcG9ydCBvcwpmcm9t\nIG9wZW5haSBpbXBvcnQgT3BlbkFJCgpjbGFzcyBPcGVuQUlDbGllbnQ6CiAg\nICBkZWYgX19pbml0X18oc2VsZik6CiAgICAgICAgIyBMb2FkIGVudmlyb25t\nZW50IHZhcmlhYmxlcwogICAgICAgIGxvYWRfZG90ZW52KCkKICAgICAgICBz\nZWxmLmNsaWVudCA9IE9wZW5BSShhcGlfa2V5PW9zLmdldGVudigiT1BFTkFJ\nX0FQSV9LRVkiKSkKICAgIAogICAgZGVmIGNyZWF0ZV9jb21wbGV0aW9uKHNl\nbGYsIG1lc3NhZ2VzKToKICAgICAgICAiIiIKICAgICAgICBDcmVhdGUgYSB0\nZXh0IGNvbXBsZXRpb24gdXNpbmcgT3BlbkFJIEFQSS4KCiAgICAgICAgQXJn\nczoKICAgICAgICBtZXNzYWdlcyAobGlzdCk6IEEgbGlzdCBvZiBkaWN0aW9u\nYXJpZXMgZGVmaW5pbmcgdGhlIGludGVyYWN0aW9uIGhpc3RvcnksCiAgICAg\nICAgICAgICAgICAgICAgICAgICB3aGVyZSBlYWNoIGRpY3Rpb25hcnkgY29u\ndGFpbnMgJ3JvbGUnIGFuZCAnY29udGVudCcuCgogICAgICAgIFJldHVybnM6\nCiAgICAgICAgc3RyOiBUaGUgY29udGVudCBvZiB0aGUgcmVzcG9uc2UgbWVz\nc2FnZS4KICAgICAgICAiIiIKICAgICAgICBjb21wbGV0aW9uID0gc2VsZi5j\nbGllbnQuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUoCiAgICAgICAgICAgIG1v\nZGVsPSJncHQtMy41LXR1cmJvIiwKICAgICAgICAgICAgbWVzc2FnZXM9bWVz\nc2FnZXMKICAgICAgICApCiAgICAgICAgcmV0dXJuIGNvbXBsZXRpb24uY2hv\naWNlc1swXS5tZXNzYWdlLmNvbnRlbnQuc3RyaXAoKQoKZGVmIGdldF9hbnN3\nZXIoY2xpZW50LCBzeXN0ZW0sIHByb21wdCk6CiAgICBtZXNzYWdlcyA9IFsK\nICAgICAgICB7InJvbGUiOiAic3lzdGVtIiwgImNvbnRlbnQiOiBzeXN0ZW19\nLAogICAgICAgIHsicm9sZSI6ICJ1c2VyIiwgImNvbnRlbnQiOiBwcm9tcHR9\nCiAgICBdCiAgICByZXNwb25zZSA9IGNsaWVudC5jcmVhdGVfY29tcGxldGlv\nbihtZXNzYWdlcykKICAgIHJldHVybiByZXNwb25zZQoKIyBVc2FnZQppZiBf\nX25hbWVfXyA9PSAiX19tYWluX18iOgogICAgY2xpZW50ID0gT3BlbkFJQ2xp\nZW50KCkKICAgIG1lc3NhZ2VzID0gWwogICAgICAgIHsicm9sZSI6ICJzeXN0\nZW0iLCAiY29udGVudCI6ICJZb3UgYXJlIGEgaGVscGZ1bCBhc3Npc3RhbnQu\nIn0sCiAgICAgICAgeyJyb2xlIjogInVzZXIiLCAiY29udGVudCI6ICJIZWxs\nbyEifQogICAgXQogICAgcmVzcG9uc2UgPSBjbGllbnQuY3JlYXRlX2NvbXBs\nZXRpb24obWVzc2FnZXMpCiAgICBwcmludChyZXNwb25zZSkK\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/M507/RamiGPT/contents/ai.py?ref=e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1", "git": "https://api.github.com/repos/M507/RamiGPT/git/blobs/7ab937b5c5e13a923ff539867acfb7c69f37e529", "html": "https://github.com/M507/RamiGPT/blob/e75cdd80ee7f71ca348b2e45e0a65b3aad0263c1/ai.py"}} | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "op.py", "path": "op.py", "sha": "6cad03380c73bbfdf93a17bdaba308824d719cfb", "size": 1289, "url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "html_url": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "git_url": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "download_url": "https://raw.githubusercontent.com/NemroNeno/LinkedIn_messaging_agent/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py", "type": "file", "content": "IyBpbnN0YWxsIGRlcGVuZGVuY2llcyBpZiBuZWVkZWQ6CiMgcGlwIGluc3Rh\nbGwgbGFuZ2NoYWluIG9wZW5haQoKZnJvbSBsYW5nY2hhaW4gaW1wb3J0IE9w\nZW5BSQpmcm9tIGxhbmdjaGFpbi5wcm9tcHRzIGltcG9ydCBQcm9tcHRUZW1w\nbGF0ZQpmcm9tIGxhbmdjaGFpbi5jaGFpbnMgaW1wb3J0IExMTUNoYWluCmlt\ncG9ydCBvcwpmcm9tIGxhbmdjaGFpbl9vcGVuYWkgaW1wb3J0IENoYXRPcGVu\nQUkKCiMgIyBTZXQgeW91ciBBUEkga2V5CiMgb3MuZW52aXJvblsiT1BFTkFJ\nX0FQSV9LRVkiXSA9ICJZT1VSX09QRU5BSV9BUElfS0VZIgojc2stcHJvai0t\nRzVMb1ZUZWh1TmY3dkVBTkVVbEFsa0lZNGtjYWh6MVVDdlowVHNHQ05iS3RZ\nWTdrUmFGdDRPRlBDZGtzb1FNdVZLakFJVUZMWFQzQmxia0ZKWDBmNTUtM2RG\nT3h5RHJjWFkwLWZCUk95ZEpERXhBMzlpaUpodXpEUEZqekFIQzZfUVVfNTNM\ncGxtc0Z4OUJNQm5kSHJxUmYySUEKIyAjIEluaXRpYWxpemUgdGhlIE9wZW5B\nSSBtb2RlbCAoZS5nLiwgR1BULTMuNSkKIyBsbG0gPSBPcGVuQUkobW9kZWxf\nbmFtZT0iZ3B0LTMuNS10dXJibyIsIHRlbXBlcmF0dXJlPTAuNyxhcGlfa2V5\nPSJnc2tfSDdYUkxwd1ZNd2ZITkVFelM1dElXR2R5YjNGWVdjbVRFTE1YaXh1\nWHNndndYR3ZiUEdldyIpCgojIDIuIEluaXRpYWxpemUgQ2hhdE9wZW5BSSBt\nb2RlbApsbG0gPSBDaGF0T3BlbkFJKAogICAgbW9kZWw9ImdwdC0zLjUtdHVy\nYm8iLAogICAgdGVtcGVyYXR1cmU9MC43LAogICAgbWF4X3Rva2Vucz0xNTAs\nCiAgICBhcGlfa2V5PSJzay1wcm9qLVBESDVVQzA3d0hIVDFNY1hhVjRzc0pm\nb0p5WElBYU1QWTB3Y3l0bzdHOG1JZ2l6c0tDSjY2QnZvOXp4aFJqVmFHTk5G\nNENuQWZ3VDNCbGJrRkp2Z04xTGpDeUcwSVVaY2xyWk1GaTlJZ0x1SEVzdkJV\nRXFEUHliSVI2V3JDdEwxWFdrdGM0bldKSmdueWVXUmVKZHE0WGVNUmNrQSIK\nICAgIAopCgojIDMuIFByZXBhcmUgYSBwcm9tcHQgdGVtcGxhdGUKdGVtcGxh\ndGUgPSBQcm9tcHRUZW1wbGF0ZSgKICAgIGlucHV0X3ZhcmlhYmxlcz1bIm5h\nbWUiXSwKICAgIHRlbXBsYXRlPSJXcml0ZSBhIGZyaWVuZGx5IGdyZWV0aW5n\nIHRvIHtuYW1lfS4iCikKCiMgNC4gQ3JlYXRlIHRoZSBMTE1DaGFpbgpjaGFp\nbiA9IExMTUNoYWluKHByb21wdD10ZW1wbGF0ZSwgbGxtPWxsbSkKCiMgNS4g\nUnVuIHRoZSBjaGFpbgppZiBfX25hbWVfXyA9PSAiX19tYWluX18iOgogICAg\nbmFtZSA9ICJBbGljZSIKICAgIHJlc3VsdCA9IGNoYWluLmludm9rZSh7Im5h\nbWUiOiBuYW1lfSkKICAgIHByaW50KHJlc3VsdCk=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/contents/op.py?ref=f079fa7a11b1f862d931729cfd09ada267f04cfc", "git": "https://api.github.com/repos/NemroNeno/LinkedIn_messaging_agent/git/blobs/6cad03380c73bbfdf93a17bdaba308824d719cfb", "html": "https://github.com/NemroNeno/LinkedIn_messaging_agent/blob/f079fa7a11b1f862d931729cfd09ada267f04cfc/op.py"}} | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "ml.py", "path": "dev/legacy_routing_agent/ml.py", "sha": "cdfa4807c49ac31a19303e7fa741c7b8c0163138", "size": 2536, "url": "https://api.github.com/repos/kingjulio8238/Memary/contents/dev/legacy_routing_agent/ml.py?ref=b2331a2c0844d66f69acd607b9e4dbaba56552c1", "html_url": "https://github.com/kingjulio8238/Memary/blob/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "git_url": "https://api.github.com/repos/kingjulio8238/Memary/git/blobs/cdfa4807c49ac31a19303e7fa741c7b8c0163138", "download_url": "https://raw.githubusercontent.com/kingjulio8238/Memary/b2331a2c0844d66f69acd607b9e4dbaba56552c1/dev/legacy_routing_agent/ml.py", "type": "file", "content": "ZnJvbSBvcGVuYWkgaW1wb3J0IE9wZW5BSQppbXBvcnQgb3MKaW1wb3J0IHJl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\ncnNvbiBhc2tpbmcgYWJvdXQ... [truncated] | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "app.py", "path": "app.py", "sha": "ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "size": 1673, "url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "html_url": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "git_url": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "download_url": "https://raw.githubusercontent.com/mateo-velez/youtube-video-summarizer/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py", "type": "file", "content": "aW1wb3J0IHN0cmVhbWxpdCBhcyBzdApmcm9tIHV0aWxzIGltcG9ydCAqCgoK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\nLAogICAgKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/contents/app.py?ref=c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e", "git": "https://api.github.com/repos/mateo-velez/youtube-video-summarizer/git/blobs/ee2305b0d20432c001c35d7ed98a3ba8ef45ac79", "html": "https://github.com/mateo-velez/youtube-video-summarizer/blob/c7649fc0bc75eb4374c8701c07bca43dcfaa3d6e/app.py"}} | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "ui.py", "path": "ui.py", "sha": "d7e486b8152ef4029627f54b95a7423693e7017a", "size": 1685, "url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "html_url": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "git_url": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "download_url": "https://raw.githubusercontent.com/MG-Cafe/ChatGPT-Tabular-Data/da4376f62d890057da0dc4a37e56360393d87638/ui.py", "type": "file", "content": "DQppbXBvcnQgb3MNCg0Kb3MuZW52aXJvblsnT1BFTkFJX0FQSV9LRVknXSA9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\ncA0Kcm9vdC5tYWlubG9vcCgpDQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/contents/ui.py?ref=da4376f62d890057da0dc4a37e56360393d87638", "git": "https://api.github.com/repos/MG-Cafe/ChatGPT-Tabular-Data/git/blobs/d7e486b8152ef4029627f54b95a7423693e7017a", "html": "https://github.com/MG-Cafe/ChatGPT-Tabular-Data/blob/da4376f62d890057da0dc4a37e56360393d87638/ui.py"}} | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "web.py", "path": "web.py", "sha": "130ec701c9a15dfad9b293ff6f2e7278ae319571", "size": 2715, "url": "https://api.github.com/repos/aashique1915005/web-scraping/contents/web.py?ref=552d0a044c14f61a7dae9dacc2818bb9fe434bd1", "html_url": "https://github.com/aashique1915005/web-scraping/blob/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "git_url": "https://api.github.com/repos/aashique1915005/web-scraping/git/blobs/130ec701c9a15dfad9b293ff6f2e7278ae319571", "download_url": "https://raw.githubusercontent.com/aashique1915005/web-scraping/552d0a044c14f61a7dae9dacc2818bb9fe434bd1/web.py", "type": "file", "content": "aW1wb3J0IG9zDQppbXBvcnQgcmVxdWVzdHMNCmZyb20gZG90ZW52IGltcG9y\ndCBsb2FkX2RvdGVudg0KZnJvbSBiczQgaW1wb3J0IEJlYXV0aWZ1bFNvdXAN\nCmZyb20gSVB5dGhvbi5kaXNwbGF5IGltcG9ydCBNYXJrZG93biwgZGlzcGxh\neQ0KZnJvbSBvcGVuYWkgaW1wb3J0IE9wZW5BSQ0KbG9hZF9kb3RlbnYob3Zl\ncnJpZGU9VHJ1ZSkNCmFwaV9rZXkgPSBvcy5nZXRlbnYoJ09QRU5BSV9BUElf\nS0VZJykNCg0KIyBDaGVjayB0aGUga2V5DQoNCmlmIG5vdCBhcGlfa2V5Og0K\nICAgIHByaW50KCJObyBBUEkga2V5IHdhcyBmb3VuZCAgISIpDQplbGlmIG5v\ndCBhcGlfa2V5LnN0YXJ0c3dpdGgoInNrLXByb2otIik6DQogICAgcHJpbnQo\nIkFuIEFQSSBrZXkgd2FzIGZvdW5kLCBidXQgaXQgZG9lc24ndCBzdGFydCBz\nay1wcm9qLTsgICIpDQplbGlmIGFwaV9rZXkuc3RyaXAoKSAhPSBhcGlfa2V5\nOg0KICAgIHByaW50KCJBbiBBUEkga2V5IHdhcyBmb3VuZCwgYnV0IGl0IGxv\nb2tzIGxpa2UgaXQgbWlnaHQgaGF2ZSBzcGFjZSBvciB0YWIgY2hhcmFjdGVy\ncyBhdCB0aGUgc3RhcnQgb3IgZW5kICAiKQ0KZWxzZToNCiAgICBwcmludCgi\nQVBJIGtleSBmb3VuZCBhbmQgbG9va3MgZ29vZCBzbyBmYXIhIikNCg0Kb3Bl\nbmFpID0gT3BlbkFJKCkNCg0KbWVzc2FnZSA9ICJIZWxsbywgR1BUISBUaGlz\nIGlzIG15IGZpcnN0IGV2ZXIgbWVzc2FnZSB0byB5b3UhIEhpISINCnJlc3Bv\nbnNlID0gb3BlbmFpLmNoYXQuY29tcGxldGlvbnMuY3JlYXRlKG1vZGVsPSJn\ncHQtNG8tbWluaSIsIG1lc3NhZ2VzPVt7InJvbGUiOiJ1c2VyIiwgImNvbnRl\nbnQiOm1lc3NhZ2V9XSkNCnByaW50KHJlc3BvbnNlLmNob2ljZXNbMF0ubWVz\nc2FnZS5jb250ZW50KQ0KDQpoZWFkZXJzID0gew0KICJVc2VyLUFnZW50Ijog\nIk1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFw\ncGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS8x\nMTcuMC4wLjAgU2FmYXJpLzUzNy4zNiINCn0NCg0KY2xhc3MgV2Vic2l0ZToN\nCg0KICAgIGRlZiBfX2luaXRfXyhzZWxmLCB1cmwpOg0KICAgICAgICAiIiIN\nCiAgICAgICAgQ3JlYXRlIHRoaXMgV2Vic2l0ZSBvYmplY3QgZnJvbSB0aGUg\nZ2l2ZW4gdXJsIHVzaW5nIHRoZSBCZWF1dGlmdWxTb3VwIGxpYnJhcnkNCiAg\nICAgICAgIiIiDQogICAgICAgIHNlbGYudXJsID0gdXJsDQogICAgICAgIHJl\nc3BvbnNlID0gcmVxdWVzdHMuZ2V0KHVybCwgaGVhZGVycz1oZWFkZXJzKQ0K\nICAgICAgICBzb3VwID0gQmVhdXRpZnVsU291cChyZXNwb25zZS5jb250ZW50\nLCAnaHRtbC5wYXJzZXInKQ0KICAgICAgICBzZWxmLnRpdGxlID0gc291cC50\naXRsZS5zdHJpbmcgaWYgc291cC50aXRsZSBlbHNlICJObyB0aXRsZSBmb3Vu\nZCINCiAgICAgICAgZm9yIGlycmVsZXZhbnQgaW4gc291cC5ib2R5KFsic2Ny\naXB0IiwgInN0eWxlIiwgImltZyIsICJpbnB1dCJdKToNCiAgICAgICAgICAg\nIGlycmVsZXZhbnQuZGVjb21wb3NlKCkNCiAgICAgICAgc2VsZi50ZXh0ID0g\nc291cC5ib2R5LmdldF90ZXh0KHNlcGFyYXRvcj0iXG4iLCBzdHJpcD1UcnVl\nKQ0KDQpzeXN0ZW1fcHJvbXB0ID0gIllvdSBhcmUgYW4gYXNzaXN0YW50IHRo\nYXQgYW5hbHl6ZXMgdGhlIGNvbnRlbnRzIG9mIGEgd2Vic2l0ZSBcDQphbmQg\ncHJvdmlkZXMgYSBzaG9ydCBzdW1tYXJ5LCBpZ25vcmluZyB0ZXh0IHRoYXQg\nbWlnaHQgYmUgbmF2aWdhdGlvbiByZWxhdGVkLiBcDQpSZXNwb25kIGluIG1h\ncmtkb3duLiINCg0KZGVmIHVzZXJfcHJvbXB0X2Zvcih3ZWJzaXRlKToNCiAg\nICB1c2VyX3Byb21wdCA9IGYiWW91IGFyZSBsb29raW5nIGF0IGEgd2Vic2l0\nZSB0aXRsZWQge3dlYnNpdGUudGl0bGV9Ig0KICAgIHVzZXJfcHJvbXB0ICs9\nICJcblRoZSBjb250ZW50cyBvZiB0aGlzIHdlYnNpdGUgaXMgYXMgZm9sbG93\nczsgXA0KcGxlYXNlIHByb3ZpZGUgYSBzaG9ydCBzdW1tYXJ5IG9mIHRoaXMg\nd2Vic2l0ZSBpbiBtYXJrZG93bi4gXA0KVG90YWwgZW1wbG95ZWUgY291bnQg\nPyBcDQpJZiBpdCBpbmNsdWRlcyBuZXdzIG9yIGFubm91bmNlbWVudHMsIHRo\nZW4gc3VtbWFyaXplIHRoZXNlIHRvby5cblxuIg0KICAgIHVzZXJfcHJvbXB0\nICs9IHdlYnNpdGUudGV4dA0KICAgIHJldHVybiB1c2VyX3Byb21wdA0KDQpk\nZWYgbWVzc2FnZXNfZm9yKHdlYnNpdGUpOg0KICAgIHJldHVybiBbDQogICAg\nICAgIHsicm9sZSI6ICJzeXN0ZW0iLCAiY29udGVudCI6IHN5c3RlbV9wcm9t\ncHR9LA0KICAgICAgICB7InJvbGUiOiAidXNlciIsICJjb250ZW50IjogdXNl\ncl9wcm9tcHRfZm9yKHdlYnNpdGUpfQ0KICAgIF0NCg0KZGVmIHN1bW1hcml6\nZSh1cmwpOg0KICAgIHdlYnNpdGUgPSBXZWJzaXRlKHVybCkNCiAgICByZXNw\nb25zZSA9IG9wZW5haS5jaGF0LmNvbXBsZXRpb25zLmNyZWF0ZSgNCiAgICAg\nICAgbW9kZWwgPSAiZ3B0LTRv... [truncated] | service=scanner
2025-08-14 14:58:42 - services.scanner - DEBUG - GitHub search result: {"name": "main.py", "path": "scripts/main.py", "sha": "5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "size": 2284, "url": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "html_url": "https://github.com/mckaywrigley/naval-gpt/blob/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "git_url": "https://api.github.com/repos/mckaywrigley/naval-gpt/git/blobs/5eac3228a17c3a3b8812e6d0c3ad05a8cfbc5e23", "download_url": "https://raw.githubusercontent.com/mckaywrigley/naval-gpt/89ce147352eabaa11780a0349fd9b2aad5363e5e/scripts/main.py", "type": "file", "content": "aW1wb3J0IG9zCmltcG9ydCBvcGVuYWkKZnJvbSBweWR1YiBpbXBvcnQgQXVk\naW9TZWdtZW50CmltcG9ydCB0aW1lCmltcG9ydCBqc29uCgpvcGVuYWkuYXBp\nX2tleSA9IG9zLmdldGVudigiT1BFTkFJX0FQSV9LRVkiKQoKcG9kY2FzdCA9\nIEF1ZGlvU2VnbWVudC5mcm9tX21wMygiLi4vcHVibGljL3BvZGNhc3QubXAz\nIikKCm9uZV9taW4gPSA2MCAqIDEwMDAKcG9kY2FzdF9sZW5ndGhfc2Vjb25k\ncyA9IGxlbihwb2RjYXN0KSAvIDEwMDAKY2xpcF9jb3VudCA9IChwb2RjYXN0\nX2xlbmd0aF9zZWNvbmRzIC8gNjApICsgMQoKY2xpcHMgPSBbXQoKZGVmIGNy\nZWF0ZV9jbGlwcygpOgogICAgcHJpbnQoIkNyZWF0aW5nIGNsaXBzLi4uIikK\nCiAgICBjbGlwID0gcG9kY2FzdFswOm9uZV9taW5dCiAgICBjbGlwLmV4cG9y\ndCgiY2xpcHMvMS5tcDMiLCBmb3JtYXQ9Im1wMyIpCiAgICBwcmludCgiRXhw\nb3J0ZWQgY2xpcCAxIikKICAgIAogICAgZm9yIGkgaW4gcmFuZ2UoMSwgaW50\nKGNsaXBfY291bnQpKToKICAgICAgICAgICAgZmlsZV9uYW1lID0gc3RyKGkg\nKyAxKSArICIubXAzIgogICAgICAgICAgICBjbGlwID0gcG9kY2FzdFtpICog\nb25lX21pbiAtIDEwMDA6KGkgKyAxKSAqIG9uZV9taW5dCiAgICAgICAgICAg\nIGNsaXAuZXhwb3J0KCJjbGlwcy8iICsgZmlsZV9uYW1lLCBmb3JtYXQ9Im1w\nMyIpCiAgICAgICAgICAgIHByaW50KCJFeHBvcnRlZCBjbGlwICIgKyBzdHIo\naSArIDEpKQoKZGVmIGdlbmVyYXRlX3RyYW5zY3JpcHQoKToKICAgIHByaW50\nKCJHZW5lcmF0aW5nIHRyYW5zY3JpcHQuLi4iKQoKICAgIGZvciBpIGluIHJh\nbmdlKDAsIGludChjbGlwX2NvdW50KSk6CiAgICAgICAgcHJpbnQoIlRyYW5z\nY3JpYmluZyBjbGlwICIgKyBzdHIoaSArIDEpICsgIi4uLiIpCiAgICAgICAg\nYXVkaW9fZmlsZSA9IG9wZW4oImNsaXBzLyIgKyBzdHIoaSArIDEpICsgIi5t\ncDMiLCAicmIiKQogICAgICAgIHByb21wdCA9ICJUaGUgdHJhbnNjcmlwdCBp\ncyBhIHBvZGNhc3QgYmV0d2VlbiBOYXZhbCBSYXZpa2FudCBhbmQgTml2aSBS\nYXZpa2FudCBhYm91dCBOYXZhbCdzIHBvcHVsYXIgVHdpdHRlciB0aHJlYWQg\nXCJIb3cgVG8gR2V0IFJpY2hcIiBOaXZpIGFza3MgTmF2YWwgcXVlc3Rpb25z\nIGFzIHRoZXkgZ28gdGhyb3VnaCB0aGUgdGhyZWFkLiIKCiAgICAgICAgdHJh\nbnNjcmlwdCA9IG9wZW5haS5BdWRpby50cmFuc2NyaWJlKCJ3aGlzcGVyLTEi\nLCBhdWRpb19maWxlLCBwcm9tcHQpCgogICAgICAgIGlmIHRyYW5zY3JpcHQu\ndGV4dDoKICAgICAgICAgICAgdGV4dCA9IHRyYW5zY3JpcHQudGV4dAogICAg\nICAgICAgICB0ZXh0ID0gdGV4dC5yZXBsYWNlKCJuaXZhbGQiLCAibmF2YWwi\nKS5yZXBsYWNlKCJOaXZhbGQiLCAiTmF2YWwiKQogICAgICAgICAgICBwcmlu\ndCgiXG5cblRyYW5zY3JpYmVkIHRleHQ6XG5cbiIgKyB0ZXh0KQoKICAgICAg\nICAgICAgdGltZXN0YW1wID0gaSAqIDYwCgogICAgICAgICAgICBjbGlwID0g\newogICAgICAgICAgICAgICAgImZpbGUiOiBzdHIoaSArIDEpICsgIi5tcDMi\nLAogICAgICAgICAgICAgICAgInNlY29uZHMiOiB0aW1lc3RhbXAsCiAgICAg\nICAgICAgICAgICAiY29udGVudCI6IHRleHQKICAgICAgICAgICAgfQoKICAg\nICAgICAgICAgY2xpcHMuYXBwZW5kKGNsaXApCgogICAgICAgICAgICBwcmlu\ndCgiV2FpdGluZyAxLjJzIGJlZm9yZSBuZXh0IHRyYW5zY3JpcHRpb24uLi4i\nKQogICAgICAgICAgICB0aW1lLnNsZWVwKDEuMikKICAgICAgICBlbHNlOgog\nICAgICAgICAgICBwcmludCgnRVJST1I6JyArIHN0cihpICsgMSkpCgogICAg\nICAgICAgICBjbGlwID0gewogICAgICAgICAgICAgICAgImZpbGUiOiBzdHIo\naSArIDEpICsgIi5tcDMiLAogICAgICAgICAgICAgICAgInNlY29uZHMiOiB0\naW1lc3RhbXAsCiAgICAgICAgICAgICAgICAiY29udGVudCI6ICJFUlJPUiIK\nICAgICAgICAgICAgfQoKICAgICAgICAgICAgY2xpcHMuYXBwZW5kKGNsaXAp\nCgogICAgICAgICAgICBwcmludCgiV2FpdGluZyAxMHMgYmVmb3JlIG5leHQg\ndHJhbnNjcmlwdGlvbi4uLiIpCiAgICAgICAgICAgIHRpbWUuc2xlZXAoMTAp\nCgpkZWYgY3JlYXRlX2pzb24oKToKICAgIHByaW50KCJDcmVhdGluZyBKU09O\nLi4uIikKCiAgICB3aXRoIG9wZW4oImNsaXBzLmpzb24iLCAidyIpIGFzIGY6\nCiAgICAgICAganNvbl9zdHJpbmcgPSBqc29uLmR1bXBzKGNsaXBzKQogICAg\nICAgIGYud3JpdGUoanNvbl9zdHJpbmcpCgoKCmNyZWF0ZV9jbGlwcygpCmdl\nbmVyYXRlX3RyYW5zY3JpcHQoKQpjcmVhdGVfanNvbigpCg==\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/mckaywrigley/naval-gpt/contents/scripts/main.py?ref=89ce147352eabaa11780a0349fd9b2aad5363e5e", "git": "https://api.github.com/repos/mck... [truncated] | service=scanner
2025-08-14 14:58:43 - services.scanner - DEBUG - GitHub search result: {"name": "s3.py", "path": "AI_generate/s3.py", "sha": "8ca03454c32329775527097717af74c6cb489dbf", "size": 1595, "url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "html_url": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "git_url": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "download_url": "https://raw.githubusercontent.com/nogibjj/Detecting-AI-Generated-Fake-Images/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py", "type": "file", "content": "aW1wb3J0IGJvdG8zCmltcG9ydCBpbwpmcm9tIFBJTCBpbXBvcnQgSW1hZ2UK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\nZShpbnB1dF9pbWFnZV9wYXRoKQo=\n", "encoding": "base64", "_links": {"self": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/contents/AI_generate/s3.py?ref=289903a0fb045b1364a705758ed6b63778e7418a", "git": "https://api.github.com/repos/nogibjj/Detecting-AI-Generated-Fake-Images/git/blobs/8ca03454c32329775527097717af74c6cb489dbf", "html": "https://github.com/nogibjj/Detecting-AI-Generated-Fake-Images/blob/289903a0fb045b1364a705758ed6b63778e7418a/AI_generate/s3.py"}} | service=scanner
2025-08-14 14:59:18 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 100, 'found_keys': 74, 'saved_keys': 0} | service=scanner
2025-08-14 14:59:20 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 100, 'found_keys': 7, 'saved_keys': 0} | service=scanner
2025-08-14 14:59:20 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:03:36 - root - INFO - Logging configuration initialized
2025-08-14 15:03:36 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:03:36 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:03:36 - __main__ - INFO - Starting API key verifier...
2025-08-14 15:03:36 - services.verifier - INFO - API key verifier service started | service=verifier
2025-08-14 15:03:36 - services.verifier - INFO - Starting verification cycle | service=verifier
2025-08-14 15:03:36 - services.verifier - INFO - Verifying 79 API keys | service=verifier
2025-08-14 15:03:36 - services.verifier - INFO - Processing batch 1 (50 keys) | service=verifier
2025-08-14 15:03:49 - services.verifier - INFO - Processing batch 2 (29 keys) | service=verifier
2025-08-14 15:03:55 - services.verifier - INFO - Verification cycle completed: processed=79, valid=0, invalid=79, errors=0, no_longer_working=0 | service=verifier
2025-08-14 15:33:31 - root - INFO - Logging configuration initialized
2025-08-14 15:33:31 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:33:31 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:33:31 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:33:31 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:33:31 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:33:31 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:33:31 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:33:31 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:33:32 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:33:33 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:33:33 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:37:03 - root - INFO - Logging configuration initialized
2025-08-14 15:37:03 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:37:03 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:37:03 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:37:03 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:37:03 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:37:03 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:37:03 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:37:03 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:37:04 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:38:05 | service=scanner | query_id=1
2025-08-14 15:37:04 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:38:05 | service=scanner | query_id=5
2025-08-14 15:37:04 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:37:04 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:37:04 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:42:00 - root - INFO - Logging configuration initialized
2025-08-14 15:42:00 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:42:00 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:42:00 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:42:00 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:42:00 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:42:00 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:42:00 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:42:00 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:42:01 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:43:03 | service=scanner | query_id=5
2025-08-14 15:42:01 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:43:03 | service=scanner | query_id=1
2025-08-14 15:42:02 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:42:02 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:42:02 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:42:24 - root - INFO - Logging configuration initialized
2025-08-14 15:42:24 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:42:24 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:42:24 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:42:24 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:42:24 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:42:25 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:42:25 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:42:25 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:42:25 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:43:27 | service=scanner | query_id=5
2025-08-14 15:42:25 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:43:27 | service=scanner | query_id=1
2025-08-14 15:42:26 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:42:26 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:42:26 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:43:03 - root - INFO - Logging configuration initialized
2025-08-14 15:43:03 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:43:03 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:43:03 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:43:03 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:43:03 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:43:03 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:43:03 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:43:03 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:43:04 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:05 | service=scanner | query_id=1
2025-08-14 15:43:04 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:05 | service=scanner | query_id=5
2025-08-14 15:43:04 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:04 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:04 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:43:16 - root - INFO - Logging configuration initialized
2025-08-14 15:43:16 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:43:16 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:43:16 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:43:16 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:43:16 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:43:16 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:43:16 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:43:16 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:43:17 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:18 | service=scanner | query_id=5
2025-08-14 15:43:17 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:18 | service=scanner | query_id=1
2025-08-14 15:43:17 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:17 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:17 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:43:39 - root - INFO - Logging configuration initialized
2025-08-14 15:43:39 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:43:39 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:43:39 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:43:39 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:43:39 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:43:39 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:43:39 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:43:39 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:43:40 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:41 | service=scanner | query_id=1
2025-08-14 15:43:40 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:40 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 07:44:42 | service=scanner | query_id=5
2025-08-14 15:43:40 - services.scanner - INFO - Query 'OPENAI_API_KEY language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 15:43:40 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 15:43:54 - root - INFO - Logging configuration initialized
2025-08-14 15:43:54 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 15:43:54 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 15:43:54 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 15:43:54 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 15:43:54 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 15:43:54 - services.scanner - INFO - Processing 2 queries | service=scanner
2025-08-14 15:43:54 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:43:54 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:43:56 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 15:43:56 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 70EF:3E279F:3575E3:3F0D59:689D93BD and timestamp 2025-08-14 07:43:57 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.70 seconds...
2025-08-14 15:43:56 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-14 15:43:56 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-14 15:43:56 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 6770:19B02D:352681:3EBCE5:689D93BD and timestamp 2025-08-14 07:43:57 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.62 seconds...
2025-08-14 15:43:56 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-14 15:43:57 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:43:58 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:43:58 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 15:43:58 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 6F51:218C57:350F6F:3EA74B:689D93BF and timestamp 2025-08-14 07:43:59 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 3.17 seconds...
2025-08-14 15:43:58 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-14 15:43:58 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-14 15:43:58 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 7006:32F723:33C191:3D5848:689D93BF and timestamp 2025-08-14 07:44:00 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 3.64 seconds...
2025-08-14 15:43:58 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-14 15:44:01 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 15:44:02 - services.scanner - INFO - Processing query: OPENAI_API_KEY language:python | service=scanner | query_id=5
2025-08-14 15:44:02 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 15:44:02 - __main__ - INFO - Received signal 2, initiating shutdown...
2025-08-14 15:44:02 - services.scanner - ERROR - Error processing query 'sk-proj- language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-14 15:44:03 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=5
2025-08-14 15:44:03 - services.scanner - ERROR - Error processing query 'OPENAI_API_KEY language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-14 15:44:03 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 16:18:02 - root - INFO - Logging configuration initialized
2025-08-14 16:18:02 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 16:18:02 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 16:18:02 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 16:18:02 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 16:18:02 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 16:18:02 - services.scanner - INFO - Processing 1 queries | service=scanner
2025-08-14 16:18:02 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:18:07 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 16:18:07 - utils.retry - WARNING - Attempt 1/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 670F:32F723:387929:42E40D:689D9BC0 and timestamp 2025-08-14 08:18:08 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 1.37 seconds...
2025-08-14 16:18:07 - utils.retry - WARNING - GitHub rate limit hit, attempt 1
2025-08-14 16:18:08 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:18:09 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 16:18:09 - utils.retry - WARNING - Attempt 2/3 failed for _process_query: 403 {"message": "API rate limit exceeded for user ID 69901697. If you reach out to GitHub Support for help, please include the request ID 6B73:388745:3EEF40:495A83:689D9BC3 and timestamp 2025-08-14 08:18:11 UTC.", "documentation_url": "https://docs.github.com/rest/overview/rate-limits-for-the-rest-api", "status": "403"}. Retrying in 3.77 seconds...
2025-08-14 16:18:09 - utils.retry - WARNING - GitHub rate limit hit, attempt 2
2025-08-14 16:18:13 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:18:14 - services.scanner - WARNING - Rate limit exceeded, will retry with different token | service=scanner | query_id=1
2025-08-14 16:18:14 - services.scanner - ERROR - Error processing query 'sk-proj- language:python...': Function _process_query failed after 3 attempts | service=scanner
2025-08-14 16:18:14 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 16:29:33 - root - INFO - Logging configuration initialized
2025-08-14 16:29:33 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 16:29:33 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 16:29:33 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 16:29:33 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 16:29:33 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 16:29:33 - services.scanner - INFO - Processing 1 queries | service=scanner
2025-08-14 16:29:33 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:29:35 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 08:30:36 | service=scanner | query_id=1
2025-08-14 16:29:35 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 16:29:35 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 16:30:56 - root - INFO - Logging configuration initialized
2025-08-14 16:30:56 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 16:30:56 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 16:30:56 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 16:30:56 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 16:30:56 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 16:30:56 - services.scanner - INFO - Processing 1 queries | service=scanner
2025-08-14 16:30:56 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:30:57 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 08:31:58 | service=scanner | query_id=1
2025-08-14 16:30:57 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 0, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 16:30:57 - services.scanner - INFO - Scan cycle completed | service=scanner
2025-08-14 16:39:13 - root - INFO - Logging configuration initialized
2025-08-14 16:39:13 - __main__ - INFO - API Key Scanner starting up...
2025-08-14 16:39:13 - database.connection - INFO - Database initialized successfully at data/api_keys.db
2025-08-14 16:39:13 - __main__ - INFO - Starting GitHub scanner...
2025-08-14 16:39:13 - services.scanner - INFO - GitHub scanner service started | service=scanner
2025-08-14 16:39:13 - services.scanner - INFO - Starting scan cycle | service=scanner
2025-08-14 16:39:13 - services.scanner - INFO - Processing 1 queries | service=scanner
2025-08-14 16:39:13 - services.scanner - INFO - Processing query: sk-proj- language:python | service=scanner | query_id=1
2025-08-14 16:39:15 - services.scanner - DEBUG - GitHub rate limit remaining=30, reset=2025-08-14 08:40:16 | service=scanner | query_id=1
2025-08-14 16:39:15 - services.scanner - DEBUG - GitHub search result: {"name": "application-dev.yaml", "path": "src/main/resources/application-dev.yaml", "sha": "04a476c51be3186c82c57f482967a561c3098b59", "size": 2975, "url": "https://api.github.com/repos/yevhenii-poznii/DictionaryBuilder-Application-v2/contents/src/main/resources/application-dev.yaml?ref=e6be5b9d6f334f0e1b9a741570279b8ee765e75d", "html_url": "https://github.com/yevhenii-poznii/DictionaryBuilder-Application-v2/blob/e6be5b9d6f334f0e1b9a741570279b8ee765e75d/src/main/resources/application-dev.yaml", "git_url": "https://api.github.com/repos/yevhenii-poznii/DictionaryBuilder-Application-v2/git/blobs/04a476c51be3186c82c57f482967a561c3098b59", "download_url": "https://raw.githubusercontent.com/yevhenii-poznii/DictionaryBuilder-Application-v2/e6be5b9d6f334f0e1b9a741570279b8ee765e75d/src/main/resources/application-dev.yaml", "type": "file", "content": "c2VydmVyOgogIHBvcnQ6IDg0NDMKICBzZXJ2bGV0LmNvbnRleHQtcGF0aDog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\nMjQxOTIwMH0KICByZXBldGl0aW9uOgogICAgd29yZHMtdG... [truncated] | service=scanner
2025-08-14 16:39:15 - services.scanner - INFO - Query 'sk-proj- language:python...' processed: {'processed_files': 1, 'found_keys': 0, 'saved_keys': 0} | service=scanner
2025-08-14 16:39:15 - services.scanner - INFO - Scan cycle completed | service=scanner
