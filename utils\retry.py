"""
Retry utilities with exponential backoff for the API key scanner service.
"""
import time
import random
import logging
from functools import wraps
from typing import Callable, Any, Type, Tuple, Optional
import requests
from github import GithubException

logger = logging.getLogger(__name__)


class RetryError(Exception):
    """Raised when all retry attempts are exhausted."""
    
    def __init__(self, message: str, last_exception: Exception = None):
        super().__init__(message)
        self.last_exception = last_exception


class ExponentialBackoff:
    """Implements exponential backoff with jitter."""
    
    def __init__(self, base_delay: float = 1.0, max_delay: float = 60.0, 
                 multiplier: float = 2.0, jitter: bool = True):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.multiplier = multiplier
        self.jitter = jitter
    
    def get_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        delay = min(self.base_delay * (self.multiplier ** attempt), self.max_delay)
        
        if self.jitter:
            # Add random jitter to avoid thundering herd
            delay *= (0.5 + random.random() * 0.5)
        
        return delay


def retry_with_backoff(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    multiplier: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    on_retry: Optional[Callable[[int, Exception], None]] = None
):
    """
    Decorator for retrying functions with exponential backoff.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        multiplier: Backoff multiplier
        exceptions: Tuple of exceptions to retry on
        on_retry: Callback function called on each retry
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            backoff = ExponentialBackoff(base_delay, max_delay, multiplier)
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        raise RetryError(
                            f"Function {func.__name__} failed after {max_attempts} attempts",
                            last_exception
                        )
                    
                    delay = backoff.get_delay(attempt)
                    
                    logger.warning(
                        f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f} seconds..."
                    )
                    
                    if on_retry:
                        on_retry(attempt + 1, e)
                    
                    time.sleep(delay)
                except Exception as e:
                    # Non-retryable exception
                    logger.error(f"Non-retryable error in {func.__name__}: {e}")
                    raise
            
            # This should never be reached
            raise RetryError(f"Unexpected error in retry logic for {func.__name__}")
        
        return wrapper
    return decorator


def github_retry(max_attempts: int = 3):
    """Specialized retry decorator for GitHub API calls."""
    
    def should_retry(exception: Exception) -> bool:
        """Determine if GitHub exception should be retried."""
        if isinstance(exception, GithubException):
            # Retry on rate limit, server errors, and timeouts
            return exception.status in [403, 429, 500, 502, 503, 504]
        elif isinstance(exception, requests.RequestException):
            # Retry on network errors
            return True
        return False
    
    def on_retry_callback(attempt: int, exception: Exception):
        """Callback for GitHub retry attempts."""
        if isinstance(exception, GithubException) and exception.status == 403:
            logger.warning(f"GitHub rate limit hit, attempt {attempt}")
        elif isinstance(exception, GithubException) and exception.status == 429:
            logger.warning(f"GitHub secondary rate limit hit, attempt {attempt}")
    
    return retry_with_backoff(
        max_attempts=max_attempts,
        base_delay=2.0,
        max_delay=120.0,
        multiplier=2.0,
        exceptions=(GithubException, requests.RequestException),
        on_retry=on_retry_callback
    )


def api_retry(max_attempts: int = 3):
    """Specialized retry decorator for API provider calls."""
    
    def on_retry_callback(attempt: int, exception: Exception):
        """Callback for API retry attempts."""
        if isinstance(exception, requests.RequestException):
            logger.warning(f"API request failed, attempt {attempt}: {exception}")
    
    return retry_with_backoff(
        max_attempts=max_attempts,
        base_delay=1.0,
        max_delay=30.0,
        multiplier=2.0,
        exceptions=(requests.RequestException, ConnectionError, TimeoutError),
        on_retry=on_retry_callback
    )


def database_retry(max_attempts: int = 3):
    """Specialized retry decorator for database operations."""
    
    def on_retry_callback(attempt: int, exception: Exception):
        """Callback for database retry attempts."""
        logger.warning(f"Database operation failed, attempt {attempt}: {exception}")
    
    return retry_with_backoff(
        max_attempts=max_attempts,
        base_delay=0.5,
        max_delay=10.0,
        multiplier=2.0,
        exceptions=(Exception,),  # Catch all database exceptions
        on_retry=on_retry_callback
    )


class CircuitBreaker:
    """Circuit breaker pattern implementation."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                logger.info("Circuit breaker transitioning to HALF_OPEN")
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                logger.info("Circuit breaker transitioning to CLOSED")
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.warning(f"Circuit breaker transitioning to OPEN after {self.failure_count} failures")
            
            raise


def with_circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """Decorator to add circuit breaker protection to functions."""
    circuit_breaker = CircuitBreaker(failure_threshold, recovery_timeout)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            return circuit_breaker.call(func, *args, **kwargs)
        return wrapper
    return decorator


class RateLimiter:
    """Simple rate limiter implementation."""
    
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def acquire(self) -> bool:
        """Try to acquire a rate limit slot."""
        now = time.time()
        
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls 
                     if now - call_time < self.time_window]
        
        if len(self.calls) < self.max_calls:
            self.calls.append(now)
            return True
        
        return False
    
    def wait_time(self) -> float:
        """Get time to wait before next call is allowed."""
        if not self.calls:
            return 0.0
        
        oldest_call = min(self.calls)
        return max(0.0, self.time_window - (time.time() - oldest_call))


def with_rate_limit(max_calls: int, time_window: int):
    """Decorator to add rate limiting to functions."""
    rate_limiter = RateLimiter(max_calls, time_window)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            if not rate_limiter.acquire():
                wait_time = rate_limiter.wait_time()
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)
                
                if not rate_limiter.acquire():
                    raise Exception("Failed to acquire rate limit after waiting")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator
