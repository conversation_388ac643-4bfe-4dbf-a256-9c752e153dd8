I want to create a scanning service to find exposed api keys in public repositories.

The data are stored in db.

## db
table apikeys
id(int autoincrement),apikey(str),status("valid","invalid","not_tested","error","noLongerWorking"),provider("OpenAI"|"Gemini"),link(str, the repo link),first_found(timestamp,yyyy/mm/dd),last_found,last_test,descripti()

table queries
id,query,description

there are 2 part 

## scanner
scanner use github api to search for api keys
- use github rest api tosearch for apikeys using the queires.
- store the api keys in the db.
- if the api key is already in the db, update the last_found field.
- if the api key is not in the db, insert it.
- if the api key is in the db and the last_found field is older than 1 day, update the last_found field.
- if config scanner.loop is true, run the scanner every config.scanner.interval minutes.

## verifier
verifier use the api keys to verify if they are valid.
fetch for
use regex to validate the api key format. for each provider.
test the api key by using it to make a request to the provider.
update the status and last_test field.

fetch apikeys where valid=