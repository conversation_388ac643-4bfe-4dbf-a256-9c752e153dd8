"""
Database models for the API key scanner service.
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, Enum, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
import enum

Base = declarative_base()


class APIKeyStatus(enum.Enum):
    """Enumeration for API key status values."""
    NOT_TESTED = "not_tested"
    VALID = "valid"
    INVALID = "invalid"
    ERROR = "error"
    NO_LONGER_WORKING = "noLongerWorking"


class APIKey(Base):
    """Model for storing discovered API keys."""
    __tablename__ = 'apikeys'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    apikey = Column(String(255), unique=True, nullable=False, index=True)
    status = Column(Enum(APIKeyStatus), default=APIKeyStatus.NOT_TESTED, nullable=False)
    provider = Column(String(50), nullable=True, index=True)
    link = Column(Text, nullable=True)
    first_found = Column(DateTime, default=func.now(), nullable=False)
    last_found = Column(DateTime, default=func.now(), nullable=False, index=True)
    last_test = Column(DateTime, nullable=True)
    note = Column(Text, nullable=True)
    
    # Additional indexes for performance
    __table_args__ = (
        Index('idx_status_last_test', 'status', 'last_test'),
        Index('idx_provider_status', 'provider', 'status'),
    )
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, provider={self.provider}, status={self.status.value})>"


class Query(Base):
    """Model for storing GitHub search queries."""
    __tablename__ = 'queries'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    query = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self):
        return f"<Query(id={self.id}, query='{self.query[:50]}...', active={self.is_active})>"
