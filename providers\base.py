"""
Base provider class for API key validation.
"""
import re
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any
import time
import requests

logger = logging.getLogger(__name__)


class ValidationResult:
    """Result of API key validation."""
    
    def __init__(self, is_valid: bool, status: str, error_message: str = None, provider: str = None):
        self.is_valid = is_valid
        self.status = status
        self.error_message = error_message
        self.provider = provider
        self.timestamp = time.time()


class BaseProvider(ABC):
    """Base class for API key providers."""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"provider.{name.lower()}")
        self._compiled_patterns = None
    
    @property
    @abstractmethod
    def regex_patterns(self) -> List[str]:
        """Return list of regex patterns for this provider's API keys."""
        pass
    
    @property
    @abstractmethod
    def test_endpoint(self) -> str:
        """Return the API endpoint used for testing keys."""
        pass
    
    @property
    def compiled_patterns(self) -> List[re.Pattern]:
        """Return compiled regex patterns (cached)."""
        if self._compiled_patterns is None:
            self._compiled_patterns = [
                re.compile(pattern, re.IGNORECASE) 
                for pattern in self.regex_patterns
            ]
        return self._compiled_patterns
    
    def matches_pattern(self, api_key: str) -> bool:
        """
        Check if API key matches any of the provider's patterns.
        
        Args:
            api_key: The API key to validate
            
        Returns:
            True if key matches provider patterns, False otherwise
        """
        # Clean the API key (remove Bearer prefix if present)
        clean_key = api_key.strip()
        if clean_key.lower().startswith('bearer '):
            clean_key = clean_key[7:].strip()
        
        for pattern in self.compiled_patterns:
            if pattern.search(clean_key):
                return True
        return False
    
    @abstractmethod
    def validate_key(self, api_key: str) -> ValidationResult:
        """
        Validate an API key by making a test request.
        
        Args:
            api_key: The API key to validate
            
        Returns:
            ValidationResult object with validation details
        """
        pass
    
    def _make_test_request(self, api_key: str, headers: Dict[str, str] = None, 
                          params: Dict[str, Any] = None, timeout: int = 30) -> requests.Response:
        """
        Make a test request to validate the API key.
        
        Args:
            api_key: The API key to test
            headers: Additional headers for the request
            params: Query parameters for the request
            timeout: Request timeout in seconds
            
        Returns:
            Response object
        """
        if headers is None:
            headers = {}
        
        # Clean the API key
        clean_key = api_key.strip()
        if clean_key.lower().startswith('bearer '):
            clean_key = clean_key[7:].strip()
        
        try:
            response = requests.get(
                self.test_endpoint,
                headers=headers,
                params=params,
                timeout=timeout
            )
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed for {self.name}: {e}")
            raise
    
    def _handle_response(self, response: requests.Response, api_key: str) -> ValidationResult:
        """
        Handle the response from test request and return validation result.
        
        Args:
            response: The response from the test request
            api_key: The API key that was tested
            
        Returns:
            ValidationResult object
        """
        try:
            if response.status_code == 200:
                return ValidationResult(
                    is_valid=True,
                    status="valid",
                    provider=self.name
                )
            elif response.status_code == 401:
                return ValidationResult(
                    is_valid=False,
                    status="invalid",
                    error_message="Unauthorized - Invalid API key",
                    provider=self.name
                )
            elif response.status_code == 403:
                return ValidationResult(
                    is_valid=False,
                    status="invalid",
                    error_message="Forbidden - API key lacks permissions",
                    provider=self.name
                )
            elif response.status_code == 429:
                return ValidationResult(
                    is_valid=False,
                    status="error",
                    error_message="Rate limited - Cannot validate at this time",
                    provider=self.name
                )
            else:
                return ValidationResult(
                    is_valid=False,
                    status="error",
                    error_message=f"Unexpected status code: {response.status_code}",
                    provider=self.name
                )
                
        except Exception as e:
            self.logger.error(f"Error handling response for {self.name}: {e}")
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message=f"Response handling error: {str(e)}",
                provider=self.name
            )
    
    def __str__(self):
        return f"{self.name}Provider"
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(name='{self.name}')>"
