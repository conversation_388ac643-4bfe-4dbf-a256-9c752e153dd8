"""
Database connection management for the API key scanner service.
"""
import logging
import os
from contextlib import contextmanager
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import StaticPool
from database.models import Base

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and sessions."""
    
    def __init__(self, database_path="api_keys.db", pool_size=10, max_overflow=20):
        """
        Initialize database manager.
        
        Args:
            database_path: Path to SQLite database file
            pool_size: Number of connections to maintain in pool
            max_overflow: Maximum overflow connections
        """
        self.database_path = database_path
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.engine = None
        self.session_factory = None
        self.Session = None
        
    def initialize(self):
        """Initialize database engine and create tables."""
        try:
            # Create SQLite engine with connection pooling
            self.engine = create_engine(
                f"sqlite:///{self.database_path}",
                poolclass=StaticPool,
                pool_pre_ping=True,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 30
                },
                echo=False  # Set to True for SQL debugging
            )
            
            # Enable WAL mode for better concurrency
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
            
            # Create session factory
            self.session_factory = sessionmaker(bind=self.engine)
            self.Session = scoped_session(self.session_factory)
            
            # Create all tables
            Base.metadata.create_all(self.engine)
            
            logger.info(f"Database initialized successfully at {self.database_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """
        Context manager for database sessions.
        
        Yields:
            SQLAlchemy session object
        """
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_scoped_session(self):
        """
        Get a scoped session for thread-safe operations.
        
        Returns:
            Scoped session object
        """
        return self.Session()
    
    def close(self):
        """Close database connections."""
        if self.Session:
            self.Session.remove()
        if self.engine:
            self.engine.dispose()
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


def init_database(config):
    """
    Initialize database with configuration.
    
    Args:
        config: Configuration dictionary containing database settings
    """
    db_config = config.get('database', {})
    db_path = db_config.get('path', 'api_keys.db')
    pool_size = db_config.get('pool_size', 10)
    max_overflow = db_config.get('max_overflow', 20)
    
    global db_manager
    db_manager = DatabaseManager(db_path, pool_size, max_overflow)
    db_manager.initialize()
    
    return db_manager


def get_db_session():
    """
    Get database session using global manager.
    
    Returns:
        Context manager for database session
    """
    return db_manager.get_session()


def get_scoped_session():
    """
    Get scoped session using global manager.
    
    Returns:
        Scoped session object
    """
    return db_manager.get_scoped_session()
