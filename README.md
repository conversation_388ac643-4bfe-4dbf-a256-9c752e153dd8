# API Key Scanner

A comprehensive service for discovering and validating exposed API keys in public GitHub repositories. The system consists of two main components: a scanner that discovers API keys using GitHub's Code Search API, and a verifier that validates the discovered keys against their respective providers.

## Features

- **Multi-Provider Support**: Currently supports OpenAI and Google Gemini API keys, with extensible architecture for additional providers
- **GitHub Integration**: Uses GitHub Code Search API with intelligent rate limiting and token rotation
- **Database Persistence**: SQLite database with proper indexing for efficient querying
- **Multi-Threading**: Concurrent processing for both scanning and verification
- **Comprehensive Logging**: Structured logging with different levels and file rotation
- **Configuration Management**: Flexible configuration via JSON files and environment variables
- **Command-Line Interface**: Easy-to-use CLI for all operations
- **Graceful Shutdown**: Proper signal handling and resource cleanup

## Architecture

```
api_key_scanner/
├── config/
│   └── config.json          # Configuration file
├── database/
│   ├── models.py            # SQLAlchemy database models
│   ├── connection.py        # Database connection management
│   └── init_db.py          # Database initialization script
├── providers/
│   ├── base.py             # Base provider interface
│   ├── openai.py           # OpenAI provider implementation
│   └── gemini.py           # Google Gemini provider implementation
├── services/
│   ├── scanner.py          # GitHub scanner service
│   └── verifier.py         # API key verifier service
├── utils/
│   ├── config.py           # Configuration management
│   └── logging.py          # Logging utilities
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd api-key-scanner
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the application:**
   ```bash
   cp config/config.json.example config/config.json
   # Edit config/config.json with your settings
   ```

4. **Initialize the database:**
   ```bash
   python database/init_db.py
   ```

## Configuration

### Configuration File (config/config.json)

```json
{
    "database": {
        "path": "api_keys.db",
        "pool_size": 10,
        "max_overflow": 20
    },
    "github": {
        "tokens": [
            "your_github_token_1",
            "your_github_token_2"
        ],
        "rate_limit_buffer": 100
    },
    "scanner": {
        "loop": false,
        "interval": 60,
        "max_retries": 3,
        "batch_size": 100,
        "thread_count": 3
    },
    "verifier": {
        "loop": false,
        "interval": 30,
        "max_retries": 3,
        "batch_size": 50,
        "thread_count": 5
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": "logs/api_scanner.log"
    }
}
```

### Environment Variables

You can override configuration values using environment variables:

```bash
export DATABASE_PATH="custom_api_keys.db"
export GITHUB_TOKENS="token1,token2,token3"
export SCANNER_LOOP="true"
export SCANNER_INTERVAL="120"
export VERIFIER_LOOP="true"
export VERIFIER_INTERVAL="60"
export LOG_LEVEL="DEBUG"
```

### GitHub Tokens

You need GitHub Personal Access Tokens with appropriate permissions:

1. Go to GitHub Settings → Developer settings → Personal access tokens
2. Generate new token with these scopes:
   - `public_repo` (for searching public repositories)
   - `read:org` (optional, for organization repositories)
3. Add multiple tokens to avoid rate limiting

## Usage

### Command Line Interface

The application provides several commands:

#### Run Scanner Only
```bash
python main.py scan
```

#### Run Verifier Only
```bash
python main.py verify
```

#### Run Both Services
```bash
python main.py run
```

#### Manage Search Queries
```bash
# Add a new search query
python main.py add-query -q "sk-proj- language:python" -d "OpenAI project keys in Python"

# List all queries
python main.py list-queries
```

#### View Statistics
```bash
python main.py stats
```

### Service Modes

#### One-time Execution
Set `loop: false` in configuration to run services once and exit.

#### Continuous Execution
Set `loop: true` in configuration to run services continuously with specified intervals.

## Database Schema

### apikeys Table
- `id`: Primary key
- `apikey`: The discovered API key (unique)
- `status`: Validation status (not_tested, valid, invalid, error, noLongerWorking)
- `provider`: API provider (OpenAI, Gemini, etc.)
- `link`: Source URL where key was found
- `first_found`: When key was first discovered
- `last_found`: When key was last seen
- `last_test`: When key was last validated
- `note`: Additional information or error messages

### queries Table
- `id`: Primary key
- `query`: GitHub search query string
- `description`: Human-readable description
- `is_active`: Whether query is active

## Adding New Providers

To add support for a new API provider:

1. **Create provider file** in `providers/` directory:
   ```python
   # providers/newprovider.py
   from providers.base import BaseProvider, ValidationResult
   
   class NewProvider(BaseProvider):
       def __init__(self):
           super().__init__("NewProvider")
       
       @property
       def regex_patterns(self):
           return [r"new-[A-Za-z0-9]{32}"]
       
       @property
       def test_endpoint(self):
           return "https://api.newprovider.com/test"
       
       def validate_key(self, api_key: str) -> ValidationResult:
           # Implementation here
           pass
   ```

2. **Register provider** in scanner and verifier services by adding it to the providers list.

## Monitoring and Logging

### Log Files
- Application logs are written to the file specified in configuration
- Logs are rotated when they reach 10MB (keeps 5 backup files)
- Console output includes colored formatting for better readability

### Log Levels
- `DEBUG`: Detailed information for debugging
- `INFO`: General information about service operation
- `WARNING`: Warning messages for non-critical issues
- `ERROR`: Error messages for failures
- `CRITICAL`: Critical errors that may cause service shutdown

### Statistics
Use `python main.py stats` to view:
- Total number of queries and API keys
- Breakdown by status and provider
- Database health information

## Performance Considerations

### Rate Limiting
- GitHub API allows 5,000 requests per hour per token
- Use multiple tokens to increase rate limits
- Built-in rate limit detection and token rotation
- Configurable rate limit buffer to avoid hitting limits

### Threading
- Scanner and verifier use configurable thread pools
- Recommended thread counts:
  - Scanner: 3-5 threads (limited by GitHub rate limits)
  - Verifier: 5-10 threads (limited by provider APIs)

### Database Optimization
- Proper indexing on frequently queried columns
- Connection pooling for concurrent access
- WAL mode enabled for better concurrency

## Security Considerations

- **Never commit real API keys** to version control
- Store configuration files securely
- Use environment variables for sensitive data
- Regularly rotate GitHub tokens
- Monitor for false positives in discovered keys

## Troubleshooting

### Common Issues

1. **GitHub Rate Limit Exceeded**
   - Add more GitHub tokens
   - Increase rate_limit_buffer
   - Reduce scanner thread count

2. **Database Lock Errors**
   - Reduce thread counts
   - Check disk space
   - Ensure proper database permissions

3. **Provider API Timeouts**
   - Increase timeout values
   - Reduce verifier thread count
   - Check network connectivity

### Debug Mode
Run with debug logging:
```bash
python main.py --log-level DEBUG scan
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]
