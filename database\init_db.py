#!/usr/bin/env python3
"""
Database initialization script with sample queries.
"""
from database.connection import init_database, get_db_session
from database.models import Query, APIKey
from utils.config import init_config
from utils.logging import setup_logging, get_logger
import os
import sys

# Add project root to path BEFORE local imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def create_sample_queries():
    """Create sample search queries for common API key patterns."""

    sample_queries = [
        # OpenAI API Keys
        {
            "query": "sk-proj- language:python",
            "description": "OpenAI project API keys in Python files"
        },
        {
            "query": "sk-svcacct- language:javascript",
            "description": "OpenAI service account keys in JavaScript files"
        },
        {
            "query": '"sk-" AND "openai" language:python',
            "description": "OpenAI API keys with context in Python"
        },
        {
            "query": '"Bearer sk-" language:python',
            "description": "OpenAI Bearer tokens in Python files"
        },
        {
            "query": 'OPENAI_API_KEY language:python',
            "description": "OpenAI API key environment variables in Python"
        },

        # Google/Gemini API Keys
        {
            "query": "AIza language:python",
            "description": "Google API keys in Python files"
        },
        {
            "query": "AIza language:javascript",
            "description": "Google API keys in JavaScript files"
        },
        {
            "query": '"GOOGLE_API_KEY" language:python',
            "description": "Google API key environment variables in Python"
        },
        {
            "query": '"gemini" AND "AIza" language:python',
            "description": "Gemini API keys with context in Python"
        },

        # Configuration files
        {
            "query": "sk- filename:config.json",
            "description": "API keys in JSON configuration files"
        },
        {
            "query": "sk- filename:.env",
            "description": "API keys in environment files"
        },
        {
            "query": "AIza filename:config.json",
            "description": "Google API keys in JSON configuration files"
        },
        {
            "query": "AIza filename:.env",
            "description": "Google API keys in environment files"
        },

        # Docker and deployment files
        {
            "query": "sk- filename:docker-compose.yml",
            "description": "API keys in Docker Compose files"
        },
        {
            "query": "sk- filename:Dockerfile",
            "description": "API keys in Dockerfiles"
        },
        {
            "query": "AIza filename:docker-compose.yml",
            "description": "Google API keys in Docker Compose files"
        },

        # Jupyter notebooks
        {
            "query": "sk- extension:ipynb",
            "description": "API keys in Jupyter notebooks"
        },
        {
            "query": "AIza extension:ipynb",
            "description": "Google API keys in Jupyter notebooks"
        },

        # README and documentation files
        {
            "query": "sk- filename:README.md",
            "description": "API keys in README files"
        },
        {
            "query": "AIza filename:README.md",
            "description": "Google API keys in README files"
        },

        # Test files (often contain real keys by mistake)
        {
            "query": "sk- filename:test language:python",
            "description": "API keys in Python test files"
        },
        {
            "query": "AIza filename:test language:python",
            "description": "Google API keys in Python test files"
        },

        # Common variable names
        {
            "query": '"api_key" AND "sk-" language:python',
            "description": "OpenAI keys assigned to api_key variables in Python"
        },
        {
            "query": '"API_KEY" AND "AIza" language:python',
            "description": "Google keys assigned to API_KEY variables in Python"
        },

        # YAML files
        {
            "query": "sk- extension:yml",
            "description": "API keys in YAML files"
        },
        {
            "query": "sk- extension:yaml",
            "description": "API keys in YAML files"
        },
        {
            "query": "AIza extension:yml",
            "description": "Google API keys in YAML files"
        },
        {
            "query": "AIza extension:yaml",
            "description": "Google API keys in YAML files"
        },

        # Shell scripts
        {
            "query": "sk- extension:sh",
            "description": "API keys in shell scripts"
        },
        {
            "query": "AIza extension:sh",
            "description": "Google API keys in shell scripts"
        },

        # Broad searches for recently updated repositories
        {
            "query": "sk- pushed:>2024-01-01",
            "description": "OpenAI API keys in recently updated repositories"
        },
        {
            "query": "AIza pushed:>2024-01-01",
            "description": "Google API keys in recently updated repositories"
        }
    ]

    return sample_queries


def create_database_only():
    """Create database and tables without adding sample queries."""

    try:
        # Initialize configuration and logging
        config_manager = init_config()
        setup_logging(config_manager.get_logging_config())
        logger = get_logger(__name__)

        logger.info("Creating database...")

        # Ensure database directory exists
        db_config = config_manager.get_database_config()
        db_path = db_config.get('path', 'api_keys.db')
        db_dir = os.path.dirname(db_path)

        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"Created database directory: {db_dir}")

        # Check if database already exists
        if os.path.exists(db_path):
            logger.info(f"Database already exists at: {db_path}")
        else:
            logger.info(f"Creating new database at: {db_path}")

        # Initialize database (this will create the database file and tables if they don't exist)
        init_database(config_manager.to_dict())

        logger.info("Database creation completed successfully")

        # Show database info
        with get_db_session() as session:
            query_count = session.query(Query).count()
            apikey_count = session.query(APIKey).count()

            print(f"\nDatabase Information:")
            print(f"Location: {db_path}")
            print(f"Queries: {query_count}")
            print(f"API Keys: {apikey_count}")

        return True

    except Exception as e:
        print(f"Error creating database: {e}")
        return False


def initialize_database_with_queries():
    """Initialize database and populate with sample queries."""

    try:
        # Initialize configuration and logging
        config_manager = init_config()
        setup_logging(config_manager.get_logging_config())
        logger = get_logger(__name__)

        logger.info("Initializing database...")

        # Ensure database directory exists
        db_config = config_manager.get_database_config()
        db_path = db_config.get('path', 'api_keys.db')
        db_dir = os.path.dirname(db_path)

        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"Created database directory: {db_dir}")

        # Check if database already exists
        db_exists = os.path.exists(db_path)
        if db_exists:
            logger.info(f"Database already exists at: {db_path}")
        else:
            logger.info(f"Creating new database at: {db_path}")

        # Initialize database (this will create the database file and tables if they don't exist)
        init_database(config_manager.to_dict())

        # Check if queries already exist
        with get_db_session() as session:
            existing_count = session.query(Query).count()

            if existing_count > 0:
                logger.info(
                    f"Database already contains {existing_count} queries")
                try:
                    response = input(
                        "Do you want to add sample queries anyway? (y/N): ")
                    if response.lower() not in ['y', 'yes']:
                        logger.info("Skipping sample query creation")

                        # Show final statistics
                        total_queries = session.query(Query).count()
                        active_queries = session.query(Query).filter(
                            Query.is_active == True).count()
                        apikey_count = session.query(APIKey).count()

                        print(f"\nDatabase Statistics:")
                        print(f"Location: {db_path}")
                        print(f"Total queries: {total_queries}")
                        print(f"Active queries: {active_queries}")
                        print(f"API keys: {apikey_count}")

                        return True
                except (KeyboardInterrupt, EOFError):
                    logger.info("Skipping sample query creation")
                    return True

        # Create sample queries
        sample_queries = create_sample_queries()

        logger.info(f"Adding {len(sample_queries)} sample queries...")

        with get_db_session() as session:
            added_count = 0

            for query_data in sample_queries:
                # Check if query already exists
                existing = session.query(Query).filter(
                    Query.query == query_data["query"]
                ).first()

                if not existing:
                    new_query = Query(
                        query=query_data["query"],
                        description=query_data["description"],
                        is_active=True
                    )
                    session.add(new_query)
                    added_count += 1
                else:
                    logger.debug(
                        f"Query already exists: {query_data['query']}")

            session.commit()

        logger.info(
            f"Successfully added {added_count} new queries to the database")
        logger.info("Database initialization completed")

        # Show final statistics
        with get_db_session() as session:
            total_queries = session.query(Query).count()
            active_queries = session.query(Query).filter(
                Query.is_active == True).count()
            apikey_count = session.query(APIKey).count()

            print(f"\nDatabase Statistics:")
            print(f"Location: {db_path}")
            print(f"Total queries: {total_queries}")
            print(f"Active queries: {active_queries}")
            print(f"API keys: {apikey_count}")

        return True

    except Exception as e:
        print(f"Error initializing database: {e}")
        sys.exit(1)


def main():
    """Main function with command-line interface."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Initialize API Key Scanner database")
    parser.add_argument(
        '--db-only',
        action='store_true',
        help='Create database and tables only (no sample queries)'
    )
    parser.add_argument(
        '--with-queries',
        action='store_true',
        help='Create database and add sample queries'
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help='Force recreation of database (will delete existing data)'
    )

    args = parser.parse_args()

    # If no specific option is provided, ask the user
    if not args.db_only and not args.with_queries:
        print("API Key Scanner Database Initialization")
        print("=" * 40)
        print("1. Create database only (no sample queries)")
        print("2. Create database with sample queries")
        print("3. Exit")

        while True:
            try:
                choice = input("\nEnter your choice (1-3): ").strip()
                if choice == '1':
                    args.db_only = True
                    break
                elif choice == '2':
                    args.with_queries = True
                    break
                elif choice == '3':
                    print("Exiting...")
                    return 0
                else:
                    print("Invalid choice. Please enter 1, 2, or 3.")
            except KeyboardInterrupt:
                print("\nExiting...")
                return 0

    # Handle force option
    if args.force:
        try:
            config_manager = init_config()
            db_config = config_manager.get_database_config()
            db_path = db_config.get('path', 'api_keys.db')

            if os.path.exists(db_path):
                os.remove(db_path)
                print(f"Removed existing database: {db_path}")
        except Exception as e:
            print(f"Error removing existing database: {e}")
            return 1

    # Execute the chosen action
    try:
        if args.db_only:
            success = create_database_only()
        else:  # args.with_queries or default
            success = initialize_database_with_queries()

        return 0 if success else 1

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
