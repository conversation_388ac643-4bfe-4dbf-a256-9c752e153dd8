from openai import OpenAI
import sqlite3
from colorama import Fore

con = sqlite3.connect("./data/api_keys.db")
cur = con.cursor()
res = cur.execute("SELECT apikey FROM apikeys")

for api_key in res:
    client = OpenAI(api_key=api_key)
    try:
        models = client.models.list()
        print(Fore.GREEN, "working", api_key[0])
    except Exception as e:
        print(Fore.RED, "not working: ", api_key[0])
        if "'invalid_api_key'" not in str(e):
            print(Fore.YELLOW, "unknown error: ", api_key[0])
    # break
