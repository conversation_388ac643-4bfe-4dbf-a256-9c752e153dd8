I want you to implement a comprehensive API key scanning service that discovers and validates exposed API keys in public GitHub repositories. The service should consist of two main components: a scanner and a verifier, with data persistence in a database.

## Database Schema Requirements sqlite3

Create the following database tables:

**Table: apikeys**
- id (integer, auto-increment, primary key)
- apikey (string, the actual API key found)
- status (enum: "valid", "invalid", "not_tested", "error", "noLongerWorking")
- provider (enum: "OpenAI", "Gemini" - expandable for future providers)
- link (string, URL to the GitHub repository where the key was found)
- first_found (timestamp, format: YYYY-MM-DD HH:MM:SS)
- last_found (timestamp, format: YYYY-MM-DD HH:MM:SS)
- last_test (timestamp, format: YYYY-MM-DD HH:MM:SS)
- note (string, for storing error messages or additional information)

**Table: queries**
- id (integer, auto-increment, primary key)
- query (string, the search query used with GitHub API)
- description (string, human-readable description of what the query searches for)

## Component 1: Scanner Service

Implement a scanner that uses the GitHub REST API to search for API keys:

**Core Functionality:**
- Retrieve search queries from the `queries` table
- Use GitHub's Code Search API to find potential API keys matching each query
- For each discovered API key:
  - If the key doesn't exist in database: insert new record with status "not_tested"
  - If the key exists and last_found is older than 24 hours: update last_found timestamp
  - If the key exists and last_found is within 24 hours: skip (no update needed)

**Configuration Requirements:**
- Support a configuration option `scanner.loop` (boolean)
- Support a configuration option `scanner.interval` (integer, minutes)
- When `scanner.loop` is true, run the scanner continuously every `scanner.interval` minutes

## Component 2: Verifier Service

Implement a verifier that validates discovered API keys:

**Core Functionality:**
- Query database for keys with status "not_tested" or "valid" (prioritize "not_tested")
- For each API key:
  1. Use regex validation to check if the key format matches the expected pattern for each provider
  2. Make a test API call to the respective provider to verify the key works
  3. Update the database record:
     - Set appropriate status based on test results
     - Update last_test timestamp
     - If error occurs, save error details in the note field
     - If successful, confirm/update the provider field

**Provider-Specific Requirements:**
Each provider has unique API key format and test endpoint and requirements.

each provider will be store in a python file. with it's own regex and testing method.

## Technical Requirements:
- Use appropriate error handling and logging throughout
- Implement database connection pooling for performance
- Consider implementing retry logic for failed API calls
- Ensure the service can handle large volumes of API keys efficiently
- Make the system extensible for adding new API providers in the future.

Allow it to run multiple threads or batch for better performance.

Please implement this system with proper code organization, error handling, and documentation.




I want you to implement a comprehensive API key scanning service that discovers and validates exposed API keys in public GitHub repositories. The service should consist of two main components: a scanner and a verifier, with data persistence in a PostgreSQL/MySQL database.

## Database Schema Requirements

Create the following database tables with proper indexing:

**Table: apikeys**
- id (integer, auto-increment, primary key)
- apikey (varchar(255), unique index, the actual API key found)
- status (enum: "valid", "invalid", "not_tested", "error", "noLongerWorking", default: "not_tested")
- provider (varchar(50), index, initially support "OpenAI" and "Gemini")
- link (text, URL to the GitHub repository/file where the key was found)
- first_found (timestamp, format: YYYY-MM-DD HH:MM:SS, default: current timestamp)
- last_found (timestamp, format: YYYY-MM-DD HH:MM:SS, index for efficient querying)
- last_test (timestamp, format: YYYY-MM-DD HH:MM:SS, nullable)
- note (text, for storing error messages or additional information, nullable)

**Table: queries**
- id (integer, auto-increment, primary key)
- query (varchar(500), the search query used with GitHub Code Search API)
- description (text, human-readable description of what the query searches for)
- is_active (boolean, default: true, to enable/disable queries)

## Component 1: Scanner Service

Implement a Python-based scanner that uses the GitHub REST API to search for API keys:

**Core Functionality:**
- Retrieve active search queries from the `queries` table
- Use GitHub's Code Search API (`GET /search/code`) to find potential API keys matching each query
- Handle GitHub API rate limiting (5,000 requests per hour for authenticated requests)
- Extract API keys from code search results using regex patterns
- For each discovered API key:
  - If the key doesn't exist in database: insert new record with status "not_tested"
  - If the key exists and last_found is older than 24 hours: update last_found timestamp
  - If the key exists and last_found is within 24 hours: skip (no database update needed)

**Configuration Requirements:**
- Use a configuration file JSON 
- Support `SCANNER_LOOP` (boolean, default: false)
- Support `SCANNER_INTERVAL` (integer, minutes, default: 60)
- Support `GITHUB_TOKEN` (required for API authentication) (support multiple tokens for rate limiting)
- use sqlite3 for db
- When `SCANNER_LOOP` is true, run the scanner continuously every `SCANNER_INTERVAL` minutes

**Error Handling:**
- Handle GitHub API rate limit errors with exponential backoff
- Log all scanning activities with appropriate log levels
- Continue processing other queries if one fails

## Component 2: Verifier Service

Implement a Python-based verifier that validates discovered API keys:

**Core Functionality:**
- Query database for keys with status "not_tested" or "valid" (prioritize "not_tested" first)
- Process keys in batches for efficiency
- For each API key:
  1. Use regex validation to check if the key format matches expected patterns
  2. Make a test API call to the respective provider to verify the key works
  3. Update the database record atomically:
     - Set appropriate status ("valid", "invalid", "error", "noLongerWorking")
     - Update last_test timestamp to current time
     - If error occurs, save error details in the note field
     - If successful, confirm/update the provider field

**Provider-Specific Requirements:**
- Create a `providers/` directory with separate Python files for each provider
- Each provider file must implement:
  - `REGEX_PATTERN`: compiled regex for key format validation
  - `validate_key(api_key: str) -> dict`: function that tests the key and returns status/error info
- Initial providers to implement:
  - `providers/openai.py`: Test with OpenAI API endpoint
  - `providers/gemini.py`: Test with Google Gemini API endpoint

**Performance Requirements:**
- Use threading (ThreadPoolExecutor) with configurable thread count (default: 5)
- Implement connection pooling for database connections
- Add retry logic with exponential backoff for failed API calls (max 3 retries)
- Process keys in batches of 50-100 to avoid overwhelming the database

## Technical Implementation Requirements:

**Project Structure:**
```
api_key_scanner/
├── config/
│   └── config.yaml
├── database/
│   ├── __init__.py
│   ├── models.py
│   └── connection.py
├── providers/
│   ├── __init__.py
│   ├── base.py
│   ├── openai.py
│   └── gemini.py
├── services/
│   ├── __init__.py
│   ├── scanner.py
│   └── verifier.py
├── utils/
│   ├── __init__.py
│   └── logging.py
├── main.py
├── requirements.txt
└── README.md
```

**Dependencies:**
- Use SQLAlchemy for database ORM
- Use requests for HTTP calls
- Use PyGithub or direct requests for GitHub API
- Use python-dotenv for environment variable management
- Use logging module for comprehensive logging

**Error Handling & Logging:**
- Implement structured logging with different levels (DEBUG, INFO, WARNING, ERROR)
- Log all API calls, database operations, and errors
- Use try-catch blocks around all external API calls
- Implement graceful shutdown handling

**Configuration Management:**
- Support both environment variables and config files
- Validate all required configuration on startup
- Provide clear error messages for missing configuration

**Database Operations:**
- Use connection pooling (SQLAlchemy engine with pool)
- Implement proper transaction handling
- Add database migration scripts for schema creation
- Use prepared statements to prevent SQL injection

**Performance & Scalability:**
- Implement multi-threading for both scanner and verifier
- Add metrics/monitoring capabilities (optional: Prometheus metrics)
- Support graceful scaling by allowing multiple service instances
- Implement proper resource cleanup and connection management

Please implement this system with comprehensive error handling, detailed logging, unit tests, and clear documentation including setup instructions and API provider configuration examples.