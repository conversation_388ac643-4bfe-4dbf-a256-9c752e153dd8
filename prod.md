I want you to implement a comprehensive API key scanning service that discovers and validates exposed API keys in public GitHub repositories. The service should consist of two main components: a scanner and a verifier, with data persistence in a database.

## Database Schema Requirements

Create the following database tables:

**Table: apikeys**
- id (integer, auto-increment, primary key)
- apikey (string, the actual API key found)
- status (enum: "valid", "invalid", "not_tested", "error", "noLongerWorking")
- provider (enum: "OpenAI", "Gemini" - expandable for future providers)
- link (string, URL to the GitHub repository where the key was found)
- first_found (timestamp, format: YYYY-MM-DD HH:MM:SS)
- last_found (timestamp, format: YYYY-MM-DD HH:MM:SS)
- last_test (timestamp, format: YYYY-MM-DD HH:MM:SS)
- note (string, for storing error messages or additional information)

**Table: queries**
- id (integer, auto-increment, primary key)
- query (string, the search query used with GitHub API)
- description (string, human-readable description of what the query searches for)

## Component 1: Scanner Service

Implement a scanner that uses the GitHub REST API to search for API keys:

**Core Functionality:**
- Retrieve search queries from the `queries` table
- Use GitHub's Code Search API to find potential API keys matching each query
- For each discovered API key:
  - If the key doesn't exist in database: insert new record with status "not_tested"
  - If the key exists and last_found is older than 24 hours: update last_found timestamp
  - If the key exists and last_found is within 24 hours: skip (no update needed)

**Configuration Requirements:**
- Support a configuration option `scanner.loop` (boolean)
- Support a configuration option `scanner.interval` (integer, minutes)
- When `scanner.loop` is true, run the scanner continuously every `scanner.interval` minutes

## Component 2: Verifier Service

Implement a verifier that validates discovered API keys:

**Core Functionality:**
- Query database for keys with status "not_tested" or "valid" (prioritize "not_tested")
- For each API key:
  1. Use regex validation to check if the key format matches the expected pattern for each provider
  2. Make a test API call to the respective provider to verify the key works
  3. Update the database record:
     - Set appropriate status based on test results
     - Update last_test timestamp
     - If error occurs, save error details in the note field
     - If successful, confirm/update the provider field

**Provider-Specific Requirements:**
Each provider has unique API key format and test endpoint and requirements.

each provider will be store in a python file. with it's own regex and testing method.

## Technical Requirements:
- Use appropriate error handling and logging throughout
- Implement database connection pooling for performance
- Consider implementing retry logic for failed API calls
- Ensure the service can handle large volumes of API keys efficiently
- Make the system extensible for adding new API providers in the future.

Allow it to run multiple threads or batch for better performance.

Please implement this system with proper code organization, error handling, and documentation.nt