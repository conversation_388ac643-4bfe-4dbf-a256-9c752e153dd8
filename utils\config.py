"""
Configuration management for the API key scanner service.
"""
import json
import os
import logging
from typing import Dict, Any, List
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing."""
    pass


class ConfigManager:
    """Manages application configuration from files and environment variables."""
    
    def __init__(self, config_file: str = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Path to JSON configuration file
        """
        self.config_file = config_file or "config/config.json"
        self.config = {}
        self._load_env_vars()
        self._load_config_file()
        self._validate_config()
    
    def _load_env_vars(self):
        """Load environment variables."""
        load_dotenv()
        
        # Environment variable mappings
        env_mappings = {
            'DATABASE_PATH': ('database', 'path'),
            'DATABASE_POOL_SIZE': ('database', 'pool_size'),
            'DATABASE_MAX_OVERFLOW': ('database', 'max_overflow'),
            'GITHUB_TOKENS': ('github', 'tokens'),
            'GITHUB_RATE_LIMIT_BUFFER': ('github', 'rate_limit_buffer'),
            'SCANNER_LOOP': ('scanner', 'loop'),
            'SCANNER_INTERVAL': ('scanner', 'interval'),
            'SCANNER_MAX_RETRIES': ('scanner', 'max_retries'),
            'SCANNER_BATCH_SIZE': ('scanner', 'batch_size'),
            'SCANNER_THREAD_COUNT': ('scanner', 'thread_count'),
            'VERIFIER_LOOP': ('verifier', 'loop'),
            'VERIFIER_INTERVAL': ('verifier', 'interval'),
            'VERIFIER_MAX_RETRIES': ('verifier', 'max_retries'),
            'VERIFIER_BATCH_SIZE': ('verifier', 'batch_size'),
            'VERIFIER_THREAD_COUNT': ('verifier', 'thread_count'),
            'LOG_LEVEL': ('logging', 'level'),
            'LOG_FILE': ('logging', 'file'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                if section not in self.config:
                    self.config[section] = {}
                
                # Type conversion
                if key in ['pool_size', 'max_overflow', 'interval', 'max_retries', 'batch_size', 'thread_count', 'rate_limit_buffer']:
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {value}")
                        continue
                elif key in ['loop']:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif key == 'tokens' and isinstance(value, str):
                    # Split comma-separated tokens
                    value = [token.strip() for token in value.split(',') if token.strip()]
                
                self.config[section][key] = value
    
    def _load_config_file(self):
        """Load configuration from JSON file."""
        if not os.path.exists(self.config_file):
            logger.warning(f"Configuration file not found: {self.config_file}")
            return
        
        try:
            with open(self.config_file, 'r') as f:
                file_config = json.load(f)
            
            # Merge file config with environment config (env vars take precedence)
            self._merge_config(file_config, self.config)
            
            logger.info(f"Configuration loaded from {self.config_file}")
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(f"Invalid JSON in config file {self.config_file}: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading config file {self.config_file}: {e}")
    
    def _merge_config(self, source: Dict[str, Any], target: Dict[str, Any]):
        """
        Merge source config into target config.
        Target values take precedence over source values.
        """
        for key, value in source.items():
            if key not in target:
                target[key] = value
            elif isinstance(value, dict) and isinstance(target[key], dict):
                self._merge_config(value, target[key])
    
    def _validate_config(self):
        """Validate required configuration values."""
        required_sections = {
            'database': ['path'],
            'github': ['tokens'],
            'scanner': ['loop', 'interval'],
            'verifier': ['loop', 'interval'],
            'logging': ['level']
        }
        
        for section, required_keys in required_sections.items():
            if section not in self.config:
                raise ConfigurationError(f"Missing required configuration section: {section}")
            
            for key in required_keys:
                if key not in self.config[section]:
                    raise ConfigurationError(f"Missing required configuration: {section}.{key}")
        
        # Validate GitHub tokens
        github_tokens = self.config['github']['tokens']
        if not isinstance(github_tokens, list) or not github_tokens:
            raise ConfigurationError("At least one GitHub token is required")
        
        # Validate numeric values
        numeric_validations = [
            ('database', 'pool_size', 1, 100),
            ('database', 'max_overflow', 0, 200),
            ('scanner', 'interval', 1, 1440),  # 1 minute to 24 hours
            ('verifier', 'interval', 1, 1440),
            ('scanner', 'max_retries', 0, 10),
            ('verifier', 'max_retries', 0, 10),
            ('scanner', 'batch_size', 1, 1000),
            ('verifier', 'batch_size', 1, 1000),
            ('scanner', 'thread_count', 1, 20),
            ('verifier', 'thread_count', 1, 20),
        ]
        
        for section, key, min_val, max_val in numeric_validations:
            if key in self.config.get(section, {}):
                value = self.config[section][key]
                if not isinstance(value, int) or value < min_val or value > max_val:
                    raise ConfigurationError(
                        f"Invalid value for {section}.{key}: {value} "
                        f"(must be integer between {min_val} and {max_val})"
                    )
    
    def get(self, section: str, key: str = None, default=None):
        """
        Get configuration value.
        
        Args:
            section: Configuration section name
            key: Configuration key name (optional)
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        if key is None:
            return self.config.get(section, default)
        return self.config.get(section, {}).get(key, default)
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return self.config.get('database', {})
    
    def get_github_config(self) -> Dict[str, Any]:
        """Get GitHub configuration."""
        return self.config.get('github', {})
    
    def get_scanner_config(self) -> Dict[str, Any]:
        """Get scanner configuration."""
        return self.config.get('scanner', {})
    
    def get_verifier_config(self) -> Dict[str, Any]:
        """Get verifier configuration."""
        return self.config.get('verifier', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return self.config.get('logging', {})
    
    def to_dict(self) -> Dict[str, Any]:
        """Return configuration as dictionary."""
        return self.config.copy()


# Global configuration instance
config_manager = None


def init_config(config_file: str = None) -> ConfigManager:
    """
    Initialize global configuration manager.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        ConfigManager instance
    """
    global config_manager
    config_manager = ConfigManager(config_file)
    return config_manager


def get_config() -> ConfigManager:
    """
    Get global configuration manager.
    
    Returns:
        ConfigManager instance
        
    Raises:
        ConfigurationError: If configuration not initialized
    """
    if config_manager is None:
        raise ConfigurationError("Configuration not initialized. Call init_config() first.")
    return config_manager
