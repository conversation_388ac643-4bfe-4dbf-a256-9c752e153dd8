{"db": {"host": "localhost", "port": 5432, "user": "postgres", "password": "postgres", "database": "api_key_scanner"}, "github": {"tokens": ["github_token_1", "github_token_2", "github_token_3"]}, "verifier": {"loop": false, "interval": 10, "max_retries": 3, "description": "verify the apikeys. Interval in minutes"}, "scanner": {"loop": false, "interval": 10, "max_retries": 3, "description": "Search on github for api keys. Interval in minutes"}}