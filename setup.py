#!/usr/bin/env python3
"""
Setup script for the API Key Scanner.
"""
import os
import sys
import json
import shutil
from pathlib import Path

def create_directories():
    """Create necessary directories."""
    directories = [
        'logs',
        'config',
        'data'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")


def create_config_file():
    """Create configuration file from example."""
    config_path = Path('config/config.json')
    example_path = Path('config.json.example')
    
    if config_path.exists():
        print(f"✓ Configuration file already exists: {config_path}")
        return
    
    if example_path.exists():
        shutil.copy(example_path, config_path)
        print(f"✓ Created configuration file: {config_path}")
    else:
        # Create default config
        default_config = {
            "database": {
                "path": "data/api_keys.db",
                "pool_size": 10,
                "max_overflow": 20
            },
            "github": {
                "tokens": [
                    "your_github_token_here"
                ],
                "rate_limit_buffer": 100
            },
            "scanner": {
                "loop": False,
                "interval": 60,
                "max_retries": 3,
                "batch_size": 100,
                "thread_count": 3
            },
            "verifier": {
                "loop": False,
                "interval": 30,
                "max_retries": 3,
                "batch_size": 50,
                "thread_count": 5
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/api_scanner.log"
            }
        }
        
        with open(config_path, 'w') as f:
            json.dump(default_config, f, indent=4)
        
        print(f"✓ Created default configuration file: {config_path}")


def check_dependencies():
    """Check if all dependencies are installed."""
    print("Checking dependencies...")
    
    required_packages = [
        'sqlalchemy',
        'requests',
        'python-dotenv',
        'PyGithub',
        'colorlog',
        'click',
        'google-generativeai',
        'tenacity'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True


def run_installation_test():
    """Run the installation test."""
    print("\nRunning installation test...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_installation.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Installation test passed")
            return True
        else:
            print("✗ Installation test failed")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Could not run installation test: {e}")
        return False


def initialize_database():
    """Initialize the database with sample queries."""
    print("\nInitializing database...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'database/init_db.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Database initialized successfully")
            return True
        else:
            print("✗ Database initialization failed")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Could not initialize database: {e}")
        return False


def show_next_steps():
    """Show next steps to the user."""
    print("\n" + "=" * 60)
    print("SETUP COMPLETE!")
    print("=" * 60)
    
    print("\nNext steps:")
    print("1. Edit config/config.json and add your GitHub tokens:")
    print("   - Go to GitHub Settings → Developer settings → Personal access tokens")
    print("   - Generate tokens with 'public_repo' scope")
    print("   - Add them to the 'github.tokens' array in the config file")
    
    print("\n2. Test the installation:")
    print("   python test_installation.py")
    
    print("\n3. View available commands:")
    print("   python main.py --help")
    
    print("\n4. Add search queries (optional):")
    print("   python main.py add-query -q 'sk-proj- language:python' -d 'OpenAI project keys'")
    
    print("\n5. Run the scanner:")
    print("   python main.py scan")
    
    print("\n6. Run the verifier:")
    print("   python main.py verify")
    
    print("\n7. Run both services:")
    print("   python main.py run")
    
    print("\n8. View statistics:")
    print("   python main.py stats")
    
    print("\nFor more information, see README.md")


def main():
    """Main setup function."""
    print("API Key Scanner Setup")
    print("=" * 30)
    
    # Create directories
    create_directories()
    
    # Create configuration file
    create_config_file()
    
    # Check dependencies
    if not check_dependencies():
        print("\n✗ Setup incomplete due to missing dependencies")
        return 1
    
    # Run installation test
    if not run_installation_test():
        print("\n✗ Setup incomplete due to test failures")
        return 1
    
    # Initialize database
    if not initialize_database():
        print("\n⚠ Database initialization failed, but you can run it manually later")
    
    # Show next steps
    show_next_steps()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
