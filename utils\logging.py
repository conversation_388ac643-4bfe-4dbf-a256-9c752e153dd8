"""
Logging utilities for the API key scanner service.
"""
import logging
import logging.handlers
import os
import sys
from typing import Dict, Any
import colorlog


class LoggerManager:
    """Manages application logging configuration."""
    
    def __init__(self):
        self.loggers = {}
        self.configured = False
    
    def setup_logging(self, config: Dict[str, Any]):
        """
        Setup logging configuration.
        
        Args:
            config: Logging configuration dictionary
        """
        if self.configured:
            return
        
        log_level = config.get('level', 'INFO').upper()
        log_format = config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        log_file = config.get('file', 'api_scanner.log')
        
        # Create logs directory if it doesn't exist
        log_dir = os.path.dirname(log_file) if os.path.dirname(log_file) else 'logs'
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level, logging.INFO))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colors
        console_handler = colorlog.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level, logging.INFO))
        
        console_format = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_format)
        root_logger.addHandler(console_handler)
        
        # File handler with rotation
        if log_file:
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(getattr(logging, log_level, logging.INFO))
            
            file_format = logging.Formatter(
                log_format,
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_format)
            root_logger.addHandler(file_handler)
        
        # Set specific logger levels
        self._configure_specific_loggers()
        
        self.configured = True
        logging.info("Logging configuration initialized")
    
    def _configure_specific_loggers(self):
        """Configure specific logger levels."""
        # Reduce noise from external libraries
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('github').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger instance.
        
        Args:
            name: Logger name
            
        Returns:
            Logger instance
        """
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def set_level(self, level: str):
        """
        Set logging level for all loggers.
        
        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        log_level = getattr(logging, level.upper(), logging.INFO)
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        for handler in root_logger.handlers:
            handler.setLevel(log_level)


class ContextLogger:
    """Logger with context information."""
    
    def __init__(self, logger: logging.Logger, context: Dict[str, Any] = None):
        self.logger = logger
        self.context = context or {}
    
    def _format_message(self, message: str) -> str:
        """Format message with context."""
        if not self.context:
            return message
        
        context_str = " | ".join([f"{k}={v}" for k, v in self.context.items()])
        return f"{message} | {context_str}"
    
    def debug(self, message: str, **kwargs):
        """Log debug message with context."""
        self.logger.debug(self._format_message(message), **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message with context."""
        self.logger.info(self._format_message(message), **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with context."""
        self.logger.warning(self._format_message(message), **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with context."""
        self.logger.error(self._format_message(message), **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with context."""
        self.logger.critical(self._format_message(message), **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with context."""
        self.logger.exception(self._format_message(message), **kwargs)
    
    def with_context(self, **context) -> 'ContextLogger':
        """Create new logger with additional context."""
        new_context = {**self.context, **context}
        return ContextLogger(self.logger, new_context)


# Global logger manager
logger_manager = LoggerManager()


def setup_logging(config: Dict[str, Any]):
    """
    Setup application logging.
    
    Args:
        config: Logging configuration
    """
    logger_manager.setup_logging(config)


def get_logger(name: str) -> logging.Logger:
    """
    Get logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logger_manager.get_logger(name)


def get_context_logger(name: str, **context) -> ContextLogger:
    """
    Get context logger instance.
    
    Args:
        name: Logger name
        context: Context key-value pairs
        
    Returns:
        ContextLogger instance
    """
    logger = get_logger(name)
    return ContextLogger(logger, context)


def set_log_level(level: str):
    """
    Set logging level.
    
    Args:
        level: Logging level
    """
    logger_manager.set_level(level)
