"""
API key verifier service for validating discovered API keys.
"""
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import List, Dict, Any, Optional
from utils.retry import api_retry, database_retry

from database.connection import get_db_session
from database.models import APIKey, APIKeyStatus
from utils.logging import get_context_logger
from providers.openai import OpenAIProvider
from providers.gemini import GeminiProvider

logger = get_context_logger(__name__)


class APIKeyVerifier:
    """Verifies API keys by testing them against their respective providers."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.verifier_config = config.get('verifier', {})

        # Initialize providers
        self.providers = {
            'OpenAI': OpenAIProvider(),
            'Gemini': GeminiProvider()
        }

        self.batch_size = self.verifier_config.get('batch_size', 50)
        self.max_retries = self.verifier_config.get('max_retries', 3)
        self.thread_count = self.verifier_config.get('thread_count', 5)

        self.logger = get_context_logger(__name__, service="verifier")
        self.running = False

        # Statistics
        self.stats = {
            'processed': 0,
            'valid': 0,
            'invalid': 0,
            'errors': 0,
            'no_longer_working': 0
        }
        self.stats_lock = threading.Lock()

    def start(self):
        """Start the verifier service."""
        self.running = True
        self.logger.info("API key verifier service started")

        if self.verifier_config.get('loop', False):
            self._run_loop()
        else:
            self._run_once()

    def stop(self):
        """Stop the verifier service."""
        self.running = False
        self.logger.info("API key verifier service stopped")

    def _run_loop(self):
        """Run verifier in loop mode."""
        interval = self.verifier_config.get(
            'interval', 30) * 60  # Convert to seconds

        while self.running:
            try:
                self._run_once()
                if self.running:
                    self.logger.info(f"Sleeping for {interval} seconds")
                    time.sleep(interval)
            except KeyboardInterrupt:
                self.logger.info("Received interrupt signal")
                break
            except Exception as e:
                self.logger.error(f"Error in verifier loop: {e}")
                time.sleep(60)  # Wait before retrying

    def _run_once(self):
        """Run verifier once."""
        self.logger.info("Starting verification cycle")

        # Reset statistics
        with self.stats_lock:
            self.stats = {key: 0 for key in self.stats}

        # Get API keys to verify
        api_keys = self._get_keys_to_verify()
        if not api_keys:
            self.logger.info("No API keys to verify")
            return

        self.logger.info(f"Verifying {len(api_keys)} API keys")

        # Process keys in batches with threading
        batch_count = 0
        for i in range(0, len(api_keys), self.batch_size):
            if not self.running:
                break

            batch = api_keys[i:i + self.batch_size]
            batch_count += 1

            self.logger.info(
                f"Processing batch {batch_count} ({len(batch)} keys)")

            with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
                future_to_key = {
                    executor.submit(self._verify_api_key, key): key
                    for key in batch
                }

                for future in as_completed(future_to_key):
                    api_key = future_to_key[future]
                    try:
                        result = future.result()
                        self._update_statistics(result)
                    except Exception as e:
                        self.logger.error(
                            f"Error verifying key {api_key.id}: {e}")
                        self._update_statistics({'status': 'error'})

        # Log final statistics
        with self.stats_lock:
            self.logger.info(
                f"Verification cycle completed: "
                f"processed={self.stats['processed']}, "
                f"valid={self.stats['valid']}, "
                f"invalid={self.stats['invalid']}, "
                f"errors={self.stats['errors']}, "
                f"no_longer_working={self.stats['no_longer_working']}"
            )

    def _get_keys_to_verify(self) -> List[APIKey]:
        """Get API keys that need verification."""
        with get_db_session() as session:
            # Prioritize not_tested keys, then valid keys for re-verification
            not_tested = session.query(APIKey).filter(
                APIKey.status == APIKeyStatus.NOT_TESTED
            ).limit(self.batch_size * 2).all()

            if len(not_tested) < self.batch_size:
                # Get some valid keys for re-verification
                valid_keys = session.query(APIKey).filter(
                    APIKey.status == APIKeyStatus.VALID
                ).order_by(APIKey.last_test.asc()).limit(
                    self.batch_size - len(not_tested)
                ).all()
                return not_tested + valid_keys

            return not_tested

    @api_retry(max_attempts=3)
    def _verify_api_key(self, api_key: APIKey) -> Dict[str, Any]:
        """Verify a single API key."""
        key_logger = self.logger.with_context(
            key_id=api_key.id,
            provider=api_key.provider
        )

        key_logger.debug(f"Verifying API key")

        try:
            # Get appropriate provider
            provider = self._get_provider_for_key(api_key)
            if not provider:
                return self._update_key_status(
                    api_key,
                    APIKeyStatus.ERROR,
                    "No provider found for this key"
                )

            # Validate the key
            result = provider.validate_key(api_key.apikey)

            # Update database based on result
            if result.is_valid:
                status = APIKeyStatus.VALID
                note = None
            elif result.status == "invalid":
                status = APIKeyStatus.INVALID
                note = result.error_message
            elif result.status == "error":
                status = APIKeyStatus.ERROR
                note = result.error_message
            else:
                status = APIKeyStatus.ERROR
                note = f"Unknown validation result: {result.status}"

            return self._update_key_status(api_key, status, note)

        except Exception as e:
            key_logger.error(f"Unexpected error during verification: {e}")
            return self._update_key_status(
                api_key,
                APIKeyStatus.ERROR,
                f"Verification error: {str(e)}"
            )

    def _get_provider_for_key(self, api_key: APIKey) -> Optional[object]:
        """Get the appropriate provider for an API key."""
        # First try the stored provider
        if api_key.provider and api_key.provider in self.providers:
            provider = self.providers[api_key.provider]
            if provider.matches_pattern(api_key.apikey):
                return provider

        # Try all providers to find a match
        for provider in self.providers.values():
            if provider.matches_pattern(api_key.apikey):
                return provider

        return None

    @database_retry(max_attempts=3)
    def _update_key_status(self, api_key: APIKey, status: APIKeyStatus, note: str = None) -> Dict[str, Any]:
        """Update API key status in database."""
        try:
            with get_db_session() as session:
                # Get fresh instance from database
                db_key = session.query(APIKey).filter(
                    APIKey.id == api_key.id).first()
                if not db_key:
                    return {'status': 'error', 'message': 'Key not found in database'}

                # Handle status transitions
                old_status = db_key.status

                # If key was valid but now invalid, mark as no longer working
                if old_status == APIKeyStatus.VALID and status == APIKeyStatus.INVALID:
                    status = APIKeyStatus.NO_LONGER_WORKING

                # Update key
                db_key.status = status
                db_key.last_test = datetime.now()
                if note:
                    db_key.note = note

                # Update provider if we determined it
                provider = self._get_provider_for_key(api_key)
                if provider and not db_key.provider:
                    db_key.provider = provider.name

                session.commit()

                return {
                    'status': status.value,
                    'old_status': old_status.value if old_status else None,
                    'provider': db_key.provider,
                    'note': note
                }

        except Exception as e:
            self.logger.error(f"Error updating key status: {e}")
            return {'status': 'error', 'message': str(e)}

    def _update_statistics(self, result: Dict[str, Any]):
        """Update verification statistics."""
        with self.stats_lock:
            self.stats['processed'] += 1

            status = result.get('status', 'error')
            if status == 'valid':
                self.stats['valid'] += 1
            elif status == 'invalid':
                self.stats['invalid'] += 1
            elif status == 'noLongerWorking':
                self.stats['no_longer_working'] += 1
            else:
                self.stats['errors'] += 1

    def get_statistics(self) -> Dict[str, int]:
        """Get current verification statistics."""
        with self.stats_lock:
            return self.stats.copy()
