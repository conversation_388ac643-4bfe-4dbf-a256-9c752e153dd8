"""
Google Gemini API key provider implementation.
"""
import logging
import requests
from providers.base import BaseProvider, ValidationResult

logger = logging.getLogger(__name__)


class GeminiProvider(BaseProvider):
    """Provider for Google Gemini API key validation."""
    
    def __init__(self):
        super().__init__("Gemini")
    
    @property
    def regex_patterns(self):
        """Google Gemini API key patterns."""
        return [
            r"AIza[0-9A-Za-z\-_]{35}",  # Standard Google API key format
            r"AIza[0-9A-Za-z\-_]{39}",  # Alternative length
            r"Bearer\s+AIza[0-9A-Za-z\-_]{35}",  # Keys in auth headers
            r"Bearer\s+<PERSON>za[0-9A-Za-z\-_]{39}",  # Alternative length in headers
        ]
    
    @property
    def test_endpoint(self):
        """Google Gemini API endpoint for testing."""
        return "https://generativelanguage.googleapis.com/v1beta/models"
    
    def validate_key(self, api_key: str) -> ValidationResult:
        """
        Validate Google Gemini API key by making a test request.
        
        Args:
            api_key: The API key to validate
            
        Returns:
            ValidationResult object
        """
        if not self.matches_pattern(api_key):
            return ValidationResult(
                is_valid=False,
                status="invalid",
                error_message="API key format does not match Gemini patterns",
                provider=self.name
            )
        
        # Clean the API key
        clean_key = api_key.strip()
        if clean_key.lower().startswith('bearer '):
            clean_key = clean_key[7:].strip()
        
        # Google API uses query parameter for authentication
        params = {
            "key": clean_key
        }
        
        headers = {
            "User-Agent": "API-Key-Scanner/1.0"
        }
        
        try:
            response = self._make_test_request(clean_key, headers=headers, params=params)
            return self._handle_response(response, api_key)
            
        except requests.exceptions.Timeout:
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message="Request timeout",
                provider=self.name
            )
        except requests.exceptions.RequestException as e:
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message=f"Request failed: {str(e)}",
                provider=self.name
            )
        except Exception as e:
            self.logger.error(f"Unexpected error validating Gemini key: {e}")
            return ValidationResult(
                is_valid=False,
                status="error",
                error_message=f"Validation error: {str(e)}",
                provider=self.name
            )
    
    def _make_test_request(self, api_key: str, headers=None, params=None, timeout=30):
        """
        Override to use query parameter authentication for Google APIs.
        
        Args:
            api_key: The API key to test
            headers: Additional headers for the request
            params: Query parameters for the request
            timeout: Request timeout in seconds
            
        Returns:
            Response object
        """
        if headers is None:
            headers = {}
        if params is None:
            params = {}
        
        # Clean the API key
        clean_key = api_key.strip()
        if clean_key.lower().startswith('bearer '):
            clean_key = clean_key[7:].strip()
        
        # Add API key to params
        params["key"] = clean_key
        
        try:
            response = requests.get(
                self.test_endpoint,
                headers=headers,
                params=params,
                timeout=timeout
            )
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed for {self.name}: {e}")
            raise
