#!/usr/bin/env python3
"""
Main entry point for the API key scanner service.
"""
import os
import sys
import signal
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import click

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config import init_config, ConfigurationError
from utils.logging import setup_logging, get_logger
from database.connection import init_database
from services.scanner import GitHubScanner
from services.verifier import APIKeyVerifier

logger = None
shutdown_event = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    global logger
    if logger:
        logger.info(f"Received signal {signum}, initiating shutdown...")
    shutdown_event.set()


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


@click.group()
@click.option('--config', '-c', default='config/config.json', help='Configuration file path')
@click.option('--log-level', default=None, help='Override log level (DEBUG, INFO, WARNING, ERROR)')
@click.pass_context
def cli(ctx, config, log_level):
    """API Key Scanner - Discover and validate exposed API keys in GitHub repositories."""
    global logger
    
    try:
        # Initialize configuration
        config_manager = init_config(config)
        
        # Setup logging
        logging_config = config_manager.get_logging_config()
        if log_level:
            logging_config['level'] = log_level.upper()
        setup_logging(logging_config)
        
        logger = get_logger(__name__)
        logger.info("API Key Scanner starting up...")
        
        # Initialize database
        init_database(config_manager.to_dict())
        
        # Store config in context
        ctx.ensure_object(dict)
        ctx.obj['config'] = config_manager.to_dict()
        
    except ConfigurationError as e:
        click.echo(f"Configuration error: {e}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Initialization error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def scan(ctx):
    """Run the GitHub scanner to discover API keys."""
    config = ctx.obj['config']
    
    try:
        setup_signal_handlers()
        scanner = GitHubScanner(config)
        
        logger.info("Starting GitHub scanner...")
        scanner.start()
        
    except KeyboardInterrupt:
        logger.info("Scanner interrupted by user")
    except Exception as e:
        logger.error(f"Scanner error: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def verify(ctx):
    """Run the API key verifier to validate discovered keys."""
    config = ctx.obj['config']
    
    try:
        setup_signal_handlers()
        verifier = APIKeyVerifier(config)
        
        logger.info("Starting API key verifier...")
        verifier.start()
        
    except KeyboardInterrupt:
        logger.info("Verifier interrupted by user")
    except Exception as e:
        logger.error(f"Verifier error: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def run(ctx):
    """Run both scanner and verifier services concurrently."""
    config = ctx.obj['config']
    
    try:
        setup_signal_handlers()
        
        # Create services
        scanner = GitHubScanner(config)
        verifier = APIKeyVerifier(config)
        
        logger.info("Starting both scanner and verifier services...")
        
        # Run services in separate threads
        with ThreadPoolExecutor(max_workers=2) as executor:
            scanner_future = executor.submit(scanner.start)
            verifier_future = executor.submit(verifier.start)
            
            # Wait for shutdown signal or service completion
            while not shutdown_event.is_set():
                if scanner_future.done() and verifier_future.done():
                    break
                time.sleep(1)
            
            # Stop services
            scanner.stop()
            verifier.stop()
            
            # Wait for services to complete
            scanner_future.result(timeout=30)
            verifier_future.result(timeout=30)
        
        logger.info("All services stopped")
        
    except KeyboardInterrupt:
        logger.info("Services interrupted by user")
    except Exception as e:
        logger.error(f"Service error: {e}")
        sys.exit(1)


@cli.command()
@click.option('--query', '-q', required=True, help='GitHub search query')
@click.option('--description', '-d', help='Description of the query')
@click.pass_context
def add_query(ctx, query, description):
    """Add a new search query to the database."""
    from database.connection import get_db_session
    from database.models import Query
    
    try:
        with get_db_session() as session:
            new_query = Query(
                query=query,
                description=description or f"Search for: {query}",
                is_active=True
            )
            session.add(new_query)
            session.commit()
            
            click.echo(f"Query added successfully: {query}")
            logger.info(f"Added new query: {query}")
            
    except Exception as e:
        click.echo(f"Error adding query: {e}", err=True)
        logger.error(f"Error adding query: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def list_queries(ctx):
    """List all search queries in the database."""
    from database.connection import get_db_session
    from database.models import Query
    
    try:
        with get_db_session() as session:
            queries = session.query(Query).all()
            
            if not queries:
                click.echo("No queries found in database")
                return
            
            click.echo(f"Found {len(queries)} queries:")
            click.echo()
            
            for query in queries:
                status = "Active" if query.is_active else "Inactive"
                click.echo(f"ID: {query.id}")
                click.echo(f"Query: {query.query}")
                click.echo(f"Description: {query.description}")
                click.echo(f"Status: {status}")
                click.echo("-" * 50)
                
    except Exception as e:
        click.echo(f"Error listing queries: {e}", err=True)
        logger.error(f"Error listing queries: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def stats(ctx):
    """Show database statistics."""
    from database.connection import get_db_session
    from database.models import APIKey, Query, APIKeyStatus
    from sqlalchemy import func
    
    try:
        with get_db_session() as session:
            # Query statistics
            total_queries = session.query(Query).count()
            active_queries = session.query(Query).filter(Query.is_active == True).count()
            
            # API key statistics
            total_keys = session.query(APIKey).count()
            
            status_counts = session.query(
                APIKey.status, func.count(APIKey.id)
            ).group_by(APIKey.status).all()
            
            provider_counts = session.query(
                APIKey.provider, func.count(APIKey.id)
            ).group_by(APIKey.provider).all()
            
            click.echo("=== API Key Scanner Statistics ===")
            click.echo()
            click.echo(f"Queries:")
            click.echo(f"  Total: {total_queries}")
            click.echo(f"  Active: {active_queries}")
            click.echo()
            click.echo(f"API Keys:")
            click.echo(f"  Total: {total_keys}")
            click.echo()
            click.echo("By Status:")
            for status, count in status_counts:
                click.echo(f"  {status.value}: {count}")
            click.echo()
            click.echo("By Provider:")
            for provider, count in provider_counts:
                click.echo(f"  {provider or 'Unknown'}: {count}")
                
    except Exception as e:
        click.echo(f"Error getting statistics: {e}", err=True)
        logger.error(f"Error getting statistics: {e}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
