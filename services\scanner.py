"""
GitHub scanner service for discovering API keys in public repositories.
"""
import re
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Set
from github import Github, GithubException
from utils.retry import github_retry, database_retry

from database.connection import get_db_session
from database.models import APIKey, Query, APIKeyStatus
from utils.logging import get_context_logger
from providers.openai import OpenAIProvider
from providers.gemini import GeminiProvider

logger = get_context_logger(__name__)


class GitHubTokenManager:
    """Manages GitHub API tokens with rotation and rate limiting."""

    def __init__(self, tokens: List[str], rate_limit_buffer: int = 100):
        self.tokens = tokens
        self.rate_limit_buffer = rate_limit_buffer
        self.current_token_index = 0
        self.token_stats = {token: {'remaining': 5000,
                                    'reset_time': 0} for token in tokens}
        self.lock = threading.Lock()

    def get_available_token(self) -> str:
        """Get an available token with remaining rate limit."""
        with self.lock:
            current_time = time.time()

            # Check if current token is still good
            current_token = self.tokens[self.current_token_index]
            stats = self.token_stats[current_token]

            if stats['remaining'] > self.rate_limit_buffer or current_time > stats['reset_time']:
                return current_token

            # Find next available token
            for i, token in enumerate(self.tokens):
                stats = self.token_stats[token]
                if stats['remaining'] > self.rate_limit_buffer or current_time > stats['reset_time']:
                    self.current_token_index = i
                    return token

            # All tokens exhausted, wait for reset
            min_reset_time = min(stats['reset_time']
                                 for stats in self.token_stats.values())
            wait_time = max(0, min_reset_time - current_time)
            if wait_time > 0:
                logger.warning(
                    f"All tokens exhausted, waiting {wait_time:.1f} seconds")
                time.sleep(wait_time)

            return self.tokens[0]

    def update_token_stats(self, token: str, remaining: int, reset_time: int):
        """Update token rate limit statistics."""
        with self.lock:
            self.token_stats[token] = {
                'remaining': remaining,
                'reset_time': reset_time
            }


class GitHubScanner:
    """Scans GitHub repositories for API keys."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.github_config = config.get('github', {})
        self.scanner_config = config.get('scanner', {})

        # Initialize GitHub token manager
        tokens = self.github_config.get('tokens', [])
        if not tokens:
            raise ValueError("No GitHub tokens provided")

        rate_limit_buffer = self.github_config.get('rate_limit_buffer', 100)
        self.token_manager = GitHubTokenManager(tokens, rate_limit_buffer)

        # Initialize providers
        self.providers = [
            OpenAIProvider(),
            GeminiProvider()
        ]

        # Compile all regex patterns
        self.all_patterns = []
        for provider in self.providers:
            self.all_patterns.extend(provider.compiled_patterns)

        self.batch_size = self.scanner_config.get('batch_size', 100)
        self.max_retries = self.scanner_config.get('max_retries', 3)
        self.thread_count = self.scanner_config.get('thread_count', 3)

        self.logger = get_context_logger(__name__, service="scanner")
        self.running = False

    def start(self):
        """Start the scanner service."""
        self.running = True
        self.logger.info("GitHub scanner service started")

        if self.scanner_config.get('loop', False):
            self._run_loop()
        else:
            self._run_once()

    def stop(self):
        """Stop the scanner service."""
        self.running = False
        self.logger.info("GitHub scanner service stopped")

    def _run_loop(self):
        """Run scanner in loop mode."""
        interval = self.scanner_config.get(
            'interval', 60) * 60  # Convert to seconds

        while self.running:
            try:
                self._run_once()
                if self.running:
                    self.logger.info(f"Sleeping for {interval} seconds")
                    time.sleep(interval)
            except KeyboardInterrupt:
                self.logger.info("Received interrupt signal")
                break
            except Exception as e:
                self.logger.error(f"Error in scanner loop: {e}")
                time.sleep(60)  # Wait before retrying

    def _run_once(self):
        """Run scanner once."""
        self.logger.info("Starting scan cycle")

        # Get active queries
        queries = self._get_active_queries()
        if not queries:
            self.logger.warning("No active queries found")
            return

        self.logger.info(f"Processing {len(queries)} queries")

        # Process queries with threading
        with ThreadPoolExecutor(max_workers=self.thread_count) as executor:
            future_to_query = {
                executor.submit(self._process_query, query): query
                for query in queries
            }

            for future in as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    result = future.result()
                    self.logger.info(
                        f"Query '{query.query[:50]}...' processed: {result}")
                except Exception as e:
                    self.logger.error(
                        f"Error processing query '{query.query[:50]}...': {e}")

        self.logger.info("Scan cycle completed")

    def _get_active_queries(self) -> List[Query]:
        """Get active search queries from database."""
        with get_db_session() as session:
            return session.query(Query).filter(Query.is_active == True).all()

    @github_retry(max_attempts=3)
    def _process_query(self, query: Query) -> Dict[str, int]:
        """Process a single search query."""
        query_logger = self.logger.with_context(query_id=query.id)
        query_logger.info(f"Processing query: {query.query}")

        token = self.token_manager.get_available_token()
        github = Github(token)

        try:
            # Search for code
            search_results = github.search_code(query.query)

            # Update token stats
            rate_limit = github.get_rate_limit()
            self.token_manager.update_token_stats(
                token,
                rate_limit.search.remaining,
                rate_limit.search.reset.timestamp()
            )

            found_keys = set()
            processed_files = 0

            # Process search results
            for result in search_results:
                if not self.running:
                    break

                try:
                    # Extract API keys from file content
                    keys = self._extract_api_keys(result)
                    found_keys.update(keys)
                    processed_files += 1

                    if processed_files >= self.batch_size:
                        break

                except Exception as e:
                    query_logger.warning(
                        f"Error processing file {result.path}: {e}")
                    continue

            # Save found keys to database
            saved_count = self._save_api_keys(found_keys, query)

            return {
                'processed_files': processed_files,
                'found_keys': len(found_keys),
                'saved_keys': saved_count
            }

        except GithubException as e:
            if e.status == 403:
                query_logger.warning(
                    "Rate limit exceeded, will retry with different token")
                raise
            elif e.status == 422:
                query_logger.error(f"Invalid query syntax: {query.query}")
                return {'error': 'Invalid query syntax'}
            else:
                query_logger.error(f"GitHub API error: {e}")
                raise
        except Exception as e:
            query_logger.error(f"Unexpected error processing query: {e}")
            raise

    def _extract_api_keys(self, search_result) -> Set[str]:
        """Extract API keys from search result."""
        keys = set()

        try:
            # Get file content
            content = search_result.decoded_content.decode(
                'utf-8', errors='ignore')

            # Extract keys using all patterns
            for pattern in self.all_patterns:
                matches = pattern.findall(content)
                for match in matches:
                    # Clean the match
                    clean_key = match.strip()
                    if clean_key.lower().startswith('bearer '):
                        clean_key = clean_key[7:].strip()

                    # Validate key length and format
                    if len(clean_key) >= 20 and self._is_valid_key_format(clean_key):
                        keys.add(clean_key)

        except Exception as e:
            self.logger.warning(
                f"Error extracting keys from {search_result.path}: {e}")

        return keys

    def _is_valid_key_format(self, key: str) -> bool:
        """Basic validation of API key format."""
        # Check for common invalid patterns
        if key.lower() in ['your_api_key', 'api_key_here', 'your_key_here']:
            return False

        # Check for placeholder patterns
        if 'xxx' in key.lower() or 'example' in key.lower():
            return False

        # Check for repeated characters (likely fake)
        if len(set(key)) < 5:
            return False

        return True

    @database_retry(max_attempts=3)
    def _save_api_keys(self, keys: Set[str], query: Query) -> int:
        """Save discovered API keys to database."""
        if not keys:
            return 0

        saved_count = 0
        current_time = datetime.now()

        with get_db_session() as session:
            for key in keys:
                try:
                    # Check if key already exists
                    existing_key = session.query(APIKey).filter(
                        APIKey.apikey == key).first()

                    if existing_key:
                        # Update last_found if it's been more than 24 hours
                        if existing_key.last_found < current_time - timedelta(hours=24):
                            existing_key.last_found = current_time
                            saved_count += 1
                    else:
                        # Determine provider
                        provider = self._determine_provider(key)

                        # Create new API key record
                        new_key = APIKey(
                            apikey=key,
                            status=APIKeyStatus.NOT_TESTED,
                            provider=provider,
                            link=f"Found via query: {query.query}",
                            first_found=current_time,
                            last_found=current_time
                        )
                        session.add(new_key)
                        saved_count += 1

                except Exception as e:
                    self.logger.error(f"Error saving API key: {e}")
                    continue

        return saved_count

    def _determine_provider(self, api_key: str) -> str:
        """Determine which provider the API key belongs to."""
        for provider in self.providers:
            if provider.matches_pattern(api_key):
                return provider.name
        return "Unknown"
